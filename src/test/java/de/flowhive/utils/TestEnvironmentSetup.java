package de.flowhive.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Properties;

/**
 * Utility class to set up the test environment.
 */
public class TestEnvironmentSetup {

    /**
     * Sets up the environment variables needed for tests.
     * Tries to load from .env.local file if it exists, otherwise sets default values.
     *
     * Note: This method sets Java system properties, not actual environment variables.
     * The FlowHive class needs to be modified to check both.
     */
    public static void setupEnvironment() {
        // Always set development environment for tests
        System.setProperty("ENVIRONMENT", "development");

        // Set a flag to indicate we're in test mode
        System.setProperty("FLOWHIVE_TEST_MODE", "true");

        // Try to load database connection info from .env.local
        File envFile = new File(".env.local");
        if (envFile.exists()) {
            try {
                Properties props = new Properties();
                FileInputStream fis = new FileInputStream(envFile);
                props.load(fis);
                fis.close();

                // Set properties from file
                for (String key : props.stringPropertyNames()) {
                    System.setProperty(key, props.getProperty(key));
                }

                HiveLog.debug(TestEnvironmentSetup.class, "Loaded environment variables from .env.local");
            } catch (IOException e) {
                HiveLog.error(TestEnvironmentSetup.class, "Failed to load .env.local: " + e.getMessage());
                setDefaultDatabaseProperties();
            }
        } else {
            setDefaultDatabaseProperties();
        }
    }

    /**
     * Sets default database properties for testing.
     */
    private static void setDefaultDatabaseProperties() {
        // Set default database connection for tests
        System.setProperty("POSTGRESQL_URI", "*****************************************");
        System.setProperty("POSTGRES_USER", "myuser");
        System.setProperty("POSTGRES_PASSWORD", "password");

        HiveLog.debug(TestEnvironmentSetup.class, "Using default database properties for tests");
    }
}
