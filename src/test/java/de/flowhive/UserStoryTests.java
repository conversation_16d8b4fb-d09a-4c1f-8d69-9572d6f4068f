package de.flowhive;

import de.flowhive.project.PRIORITY;
import de.flowhive.project.Project;
import de.flowhive.project.ProjectManager;
import de.flowhive.project.attributes.UserStory;
import de.flowhive.user.User;
import de.flowhive.user.UserManager;
import de.flowhive.utils.TestEnvironmentSetup;
import junit.framework.TestCase;

import java.util.List;

/**
 * Test cases for user story operations.
 */
public class UserStoryTests extends TestCase {

    private ProjectManager projectManager;
    private UserManager userManager;
    private User testUser;
    private Project testProject;

    /**
     * Set up the test environment before each test.
     * Creates a test user and a test project.
     */
    @Override
    protected void setUp() throws Exception {
        super.setUp();

        // Set up environment variables for testing
        TestEnvironmentSetup.setupEnvironment();

        // Initialize FlowHive instance
        FlowHive.getInstance();

        // Initialize managers
        projectManager = FlowHive.getInstance().getProjectManager();
        userManager = FlowHive.getInstance().getUserManager();

        // Create a test user
        testUser = userManager.register("UserStoryTestUser", "Password123!", "Test", "User", "<EMAIL>");
        assertNotNull("Failed to create test user", testUser);

        // Create a test project
        testProject = projectManager.createProject(testUser.getId(), "User Story Test Project");
        assertNotNull("Failed to create test project", testProject);
    }

    /**
     * Clean up the test environment after each test.
     * Deletes the test project and test user.
     */
    @Override
    protected void tearDown() throws Exception {
        // Clean up by deleting the test project and user
        if (testProject != null) {
            projectManager.deleteProject(testProject.getId());
        }

        if (testUser != null) {
            userManager.deleteUser(testUser.getId());
        }

        super.tearDown();
    }

    /**
     * Test creating a user story with default priority.
     */
    public void testCreateUserStory() {
        // Create a user story with default priority
        UserStory userStory = projectManager.createUserStory(
                testProject.getId(),
                "Test User Story",
                "This is a test user story description"
        );

        // Verify the user story was created successfully
        assertNotNull("User story should not be null", userStory);
        assertEquals("User story name should match", "Test User Story", userStory.getName());
        assertEquals("User story description should match", "This is a test user story description", userStory.getDescription());
        assertEquals("User story priority should be MEDIUM by default", PRIORITY.MEDIUM, userStory.getPriority());

        // Verify the user story can be retrieved
        UserStory retrievedUserStory = projectManager.getUserStory(userStory.getId());
        assertNotNull("Retrieved user story should not be null", retrievedUserStory);
        assertEquals("Retrieved user story ID should match", userStory.getId(), retrievedUserStory.getId());
    }

    /**
     * Test creating a user story with a specific priority.
     */
    public void testCreateUserStoryWithPriority() {
        // Create a user story with HIGH priority
        UserStory userStory = projectManager.createUserStory(
                testProject.getId(),
                "High Priority Story",
                "This is a high priority user story",
                PRIORITY.HIGH
        );

        // Verify the user story was created successfully with the correct priority
        assertNotNull("User story should not be null", userStory);
        assertEquals("User story priority should be HIGH", PRIORITY.HIGH, userStory.getPriority());
    }

    /**
     * Test updating a user story.
     */
    public void testUpdateUserStory() {
        // Create a user story
        UserStory userStory = projectManager.createUserStory(
                testProject.getId(),
                "Original Name",
                "Original description",
                PRIORITY.LOW
        );
        assertNotNull("User story should not be null", userStory);

        // Update the user story
        userStory.setName("Updated Name");
        userStory.setDescription("Updated description");
        userStory.setPriority(PRIORITY.HIGH);

        UserStory updatedUserStory = projectManager.updateUserStory(userStory.getId(), userStory);
        assertNotNull("Updated user story should not be null", updatedUserStory);

        // Verify the updates were applied
        assertEquals("User story name should be updated", "Updated Name", updatedUserStory.getName());
        assertEquals("User story description should be updated", "Updated description", updatedUserStory.getDescription());
        assertEquals("User story priority should be updated", PRIORITY.HIGH, updatedUserStory.getPriority());
    }

    /**
     * Test deleting a user story.
     */
    public void testDeleteUserStory() {
        // Create a user story
        UserStory userStory = projectManager.createUserStory(
                testProject.getId(),
                "Story to Delete",
                "This story will be deleted"
        );
        assertNotNull("User story should not be null", userStory);

        // Verify the user story exists
        UserStory retrievedBeforeDelete = projectManager.getUserStory(userStory.getId());
        assertNotNull("User story should exist before deletion", retrievedBeforeDelete);

        // Delete the user story
        projectManager.deleteUserStory(userStory.getId());

        // Verify the user story no longer exists
        UserStory retrievedAfterDelete = projectManager.getUserStory(userStory.getId());
        assertNull("User story should not exist after deletion", retrievedAfterDelete);
    }

    /**
     * Test retrieving all user stories for a project.
     */
    public void testGetUserStoriesForProject() {
        // Create multiple user stories
        UserStory story1 = projectManager.createUserStory(testProject.getId(), "Story 1", "Description 1");
        UserStory story2 = projectManager.createUserStory(testProject.getId(), "Story 2", "Description 2");
        UserStory story3 = projectManager.createUserStory(testProject.getId(), "Story 3", "Description 3");

        // Retrieve all user stories for the project
        List<UserStory> userStories = projectManager.getUserStoriesForProject(testProject.getId());

        // Verify the list contains the created user stories
        assertNotNull("User story list should not be null", userStories);
        assertTrue("User story list should contain at least 3 items", userStories.size() >= 3);

        // Verify the list contains the specific user stories we created
        boolean foundStory1 = false;
        boolean foundStory2 = false;
        boolean foundStory3 = false;

        for (UserStory story : userStories) {
            if (story.getId() == story1.getId()) foundStory1 = true;
            if (story.getId() == story2.getId()) foundStory2 = true;
            if (story.getId() == story3.getId()) foundStory3 = true;
        }

        assertTrue("User story list should contain story 1", foundStory1);
        assertTrue("User story list should contain story 2", foundStory2);
        assertTrue("User story list should contain story 3", foundStory3);
    }

    /**
     * Test getting the project ID for a user story.
     */
    public void testGetProjectIdForUserStory() {
        // Create a user story
        UserStory userStory = projectManager.createUserStory(
                testProject.getId(),
                "Project ID Test",
                "Testing project ID retrieval"
        );
        assertNotNull("User story should not be null", userStory);

        // Get the project ID for the user story
        int projectId = projectManager.getProjectIdForUserStory(userStory.getId());

        // Verify the project ID matches the test project
        assertEquals("Project ID should match the test project", testProject.getId(), projectId);
    }
}
