package de.flowhive;

import de.flowhive.project.Project;
import de.flowhive.project.ProjectManager;
import de.flowhive.user.User;
import de.flowhive.user.UserManager;
import de.flowhive.utils.TestEnvironmentSetup;
import junit.framework.TestCase;

public class UserProjectTests extends TestCase {

    private ProjectManager proMan;
    private UserManager userMan;

    @Override
    protected void setUp() throws Exception {
        super.setUp();

        // Set up environment variables for testing
        TestEnvironmentSetup.setupEnvironment();

        // Initialize FlowHive instance
        FlowHive.getInstance();

        // Initialize managers
        this.proMan = FlowHive.getInstance().getProjectManager();
        this.userMan = FlowHive.getInstance().getUserManager();
    }

    public void testDeleteUserWithOwnedProject() {
        User user = userMan.register("TestCase-User", "1234bA#8a", "PreName", "SurName", "<EMAIL>");
        assertNotNull(user);
        System.out.println("User: " + user.getId());

        Project p = proMan.createProject(user.getId(), "Test Project");
        assertNotNull(p);
        System.out.println("Project: " + p.getId());

        userMan.deleteUser(user.getId());
        assertNull(userMan.getUser(user.getId()));
        assertNull(proMan.getProject(p.getId()));
    }
}
