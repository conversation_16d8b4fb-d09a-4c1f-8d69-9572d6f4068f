package de.flowhive;

import de.flowhive.project.PRIORITY;
import de.flowhive.project.TASKSTATUS;
import de.flowhive.project.Project;
import de.flowhive.project.ProjectManager;
import de.flowhive.project.attributes.Task;
import de.flowhive.project.attributes.UserStory;
import de.flowhive.storage.TaskDatabaseImpl;
import de.flowhive.user.User;
import de.flowhive.user.UserManager;
import de.flowhive.utils.TestEnvironmentSetup;
import junit.framework.TestCase;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Test cases for task operations.
 */
public class TaskTests extends TestCase {

    private ProjectManager projectManager;
    private UserManager userManager;
    private User testUser;
    private Project testProject;
    private UserStory testUserStory;

    /**
     * Set up the test environment before each test.
     * Creates a test user, a test project, and a test user story.
     */
    @Override
    protected void setUp() throws Exception {
        super.setUp();

        // Set up environment variables for testing
        TestEnvironmentSetup.setupEnvironment();

        // Initialize FlowHive instance
        FlowHive.getInstance();

        // Initialize managers
        projectManager = FlowHive.getInstance().getProjectManager();
        userManager = FlowHive.getInstance().getUserManager();

        // Create a test user
        testUser = userManager.register("TaskTestUser", "Password123!", "Test", "User", "<EMAIL>");
        assertNotNull("Failed to create test user", testUser);

        // Create a test project
        testProject = projectManager.createProject(testUser.getId(), "Task Test Project");
        assertNotNull("Failed to create test project", testProject);

        // Create a test user story
        testUserStory = projectManager.createUserStory(testProject.getId(), "Task Test User Story", "User story for task tests");
        assertNotNull("Failed to create test user story", testUserStory);
    }

    /**
     * Clean up the test environment after each test.
     * Deletes the test user story, test project, and test user.
     */
    @Override
    protected void tearDown() throws Exception {
        // Clean up by deleting the test user story, project, and user
        if (testUserStory != null) {
            projectManager.deleteUserStory(testUserStory.getId());
        }

        if (testProject != null) {
            projectManager.deleteProject(testProject.getId());
        }

        if (testUser != null) {
            userManager.deleteUser(testUser.getId());
        }

        super.tearDown();
    }

    /**
     * Test creating a task with basic information.
     */
    public void testCreateBasicTask() {
        // Create a basic task
        Task task = projectManager.createTask(
                testUserStory.getId(),
                "Test Task",
                "This is a test task description"
        );

        // Verify the task was created successfully
        assertNotNull("Task should not be null", task);
        assertEquals("Task name should match", "Test Task", task.getName());
        assertEquals("Task description should match", "This is a test task description", task.getDescription());
        assertEquals("Task priority should be MEDIUM by default", PRIORITY.MEDIUM, task.getPriority());
        assertEquals("Task status should be NOT_STARTED by default", TASKSTATUS.NOT_STARTED, task.getStatus());

        // Verify the task can be retrieved
        Task retrievedTask = projectManager.getTask(task.getId());
        assertNotNull("Retrieved task should not be null", retrievedTask);
        assertEquals("Retrieved task ID should match", task.getId(), retrievedTask.getId());
    }

    /**
     * Test creating a task with priority and status.
     */
    public void testCreateTaskWithPriorityAndStatus() {
        // Create a task with specific priority and status
        Task task = projectManager.createTask(
                testUserStory.getId(),
                "Priority Task",
                "Task with specific priority and status",
                PRIORITY.HIGH,
                TASKSTATUS.IN_PROGRESS
        );

        // Verify the task was created successfully with the correct priority and status
        assertNotNull("Task should not be null", task);
        assertEquals("Task priority should be HIGH", PRIORITY.HIGH, task.getPriority());
        assertEquals("Task status should be IN_PROGRESS", TASKSTATUS.IN_PROGRESS, task.getStatus());
    }

    /**
     * Test creating a task with all details.
     */
    public void testCreateTaskWithAllDetails() {
        // Create dates for the task
        Date startDate = new Date();
        Date dueDate = new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000); // One week later
        int estimatedWorkTime = 8;

        // Create a task with all details
        Task task = projectManager.createTask(
                testUserStory.getId(),
                "Detailed Task",
                "Task with all details",
                PRIORITY.HIGH,
                TASKSTATUS.IN_PROGRESS,
                startDate,
                dueDate,
                estimatedWorkTime
        );

        // Verify the task was created successfully with all details
        assertNotNull("Task should not be null", task);
        assertEquals("Task name should match", "Detailed Task", task.getName());
        assertEquals("Task priority should be HIGH", PRIORITY.HIGH, task.getPriority());
        assertEquals("Task status should be IN_PROGRESS", TASKSTATUS.IN_PROGRESS, task.getStatus());
        assertNotNull("Task start date should not be null", task.getStarDate());
        assertNotNull("Task due date should not be null", task.getDueDate());
        assertEquals("Task estimated work time should match", estimatedWorkTime, task.getEstimatedWorkTime());
    }

    /**
     * Test updating a task.
     */
    public void testUpdateTask() {
        // Create a task
        Task task = projectManager.createTask(
                testUserStory.getId(),
                "Original Task",
                "Original description",
                PRIORITY.LOW,
                TASKSTATUS.NOT_STARTED
        );
        assertNotNull("Task should not be null", task);

        // Update the task
        task.setName("Updated Task");
        task.setDescription("Updated description");
        task.setPriority(PRIORITY.HIGH);
        task.setStatus(TASKSTATUS.DONE);
        task.setActualWorkTime(5);

        Task updatedTask = projectManager.updateTask(task.getId(), task);
        assertNotNull("Updated task should not be null", updatedTask);

        // Verify the updates were applied
        assertEquals("Task name should be updated", "Updated Task", updatedTask.getName());
        assertEquals("Task description should be updated", "Updated description", updatedTask.getDescription());
        assertEquals("Task priority should be updated", PRIORITY.HIGH, updatedTask.getPriority());
        assertEquals("Task status should be updated", TASKSTATUS.DONE, updatedTask.getStatus());
        assertEquals("Task actual work time should be updated", 5, updatedTask.getActualWorkTime());
    }

    /**
     * Test deleting a task.
     */
    public void testDeleteTask() {
        // Create a task
        Task task = projectManager.createTask(
                testUserStory.getId(),
                "Task to Delete",
                "This task will be deleted"
        );
        assertNotNull("Task should not be null", task);

        // Verify the task exists
        Task retrievedBeforeDelete = projectManager.getTask(task.getId());
        assertNotNull("Task should exist before deletion", retrievedBeforeDelete);

        // Delete the task
        projectManager.deleteTask(task.getId());

        // Verify the task no longer exists
        Task retrievedAfterDelete = projectManager.getTask(task.getId());
        assertNull("Task should not exist after deletion", retrievedAfterDelete);
    }

    /**
     * Test updating task assignees.
     */
    public void testUpdateTaskAssignees() {
        // Create a task
        Task task = projectManager.createTask(
                testUserStory.getId(),
                "Assignee Task",
                "Task for testing assignees"
        );
        assertNotNull("Task should not be null", task);

        // Create a list of assignees
        List<Integer> assignees = new ArrayList<>();
        assignees.add(testUser.getId());

        // Update the task assignees
        boolean updateSuccess = projectManager.updateTaskAssignees(task.getId(), assignees);
        assertTrue("Updating task assignees should succeed", updateSuccess);

        // Verify the assignees were updated
        Task retrievedTask = projectManager.getTask(task.getId());
        assertNotNull("Retrieved task should not be null", retrievedTask);
        assertNotNull("Task assignees should not be null", retrievedTask.getAssignedTo());
        assertEquals("Task should have 1 assignee", 1, retrievedTask.getAssignedTo().size());
        assertTrue("Task should be assigned to the test user", retrievedTask.getAssignedTo().contains(testUser.getId()));
    }

    /**
     * Test retrieving all tasks for a user story.
     */
    public void testGetTasksForUserStory() {
        // Create multiple tasks
        Task task1 = projectManager.createTask(testUserStory.getId(), "Task 1", "Description 1");
        Task task2 = projectManager.createTask(testUserStory.getId(), "Task 2", "Description 2");
        Task task3 = projectManager.createTask(testUserStory.getId(), "Task 3", "Description 3");

        // Retrieve all tasks for the user story
        List<Task> tasks = projectManager.getTasksForUserStory(testUserStory.getId());

        // Verify the list contains the created tasks
        assertNotNull("Task list should not be null", tasks);
        assertTrue("Task list should contain at least 3 items", tasks.size() >= 3);

        // Verify the list contains the specific tasks we created
        boolean foundTask1 = false;
        boolean foundTask2 = false;
        boolean foundTask3 = false;

        for (Task task : tasks) {
            if (task.getId() == task1.getId()) foundTask1 = true;
            if (task.getId() == task2.getId()) foundTask2 = true;
            if (task.getId() == task3.getId()) foundTask3 = true;
        }

        assertTrue("Task list should contain task 1", foundTask1);
        assertTrue("Task list should contain task 2", foundTask2);
        assertTrue("Task list should contain task 3", foundTask3);
    }
}
