package de.flowhive;

import de.flowhive.user.User;
import de.flowhive.user.UserManager;
import de.flowhive.utils.TestEnvironmentSetup;
import junit.framework.TestCase;

/**
 * Test cases for user update operations.
 */
public class UserUpdateTests extends TestCase {

    private UserManager userManager;
    private User testUser;

    /**
     * Set up the test environment before each test.
     * Creates a test user.
     */
    @Override
    protected void setUp() throws Exception {
        super.setUp();

        // Set up environment variables for testing
        TestEnvironmentSetup.setupEnvironment();

        // Initialize FlowHive instance
        FlowHive.getInstance();

        // Initialize user manager
        userManager = FlowHive.getInstance().getUserManager();

        // Create a test user
        testUser = userManager.register("UpdateTestUser", "Password123!", "Test", "User", "<EMAIL>");
        assertNotNull("Failed to create test user", testUser);
    }

    /**
     * Clean up the test environment after each test.
     * Deletes the test user.
     */
    @Override
    protected void tearDown() throws Exception {
        // Clean up by deleting the test user
        if (testUser != null) {
            userManager.deleteUser(testUser.getId());
        }

        super.tearDown();
    }

    /**
     * Test updating user profile information.
     */
    public void testUpdateUserProfile() {
        // Update user profile
        User updatedUser = userManager.updateUser(
                testUser.getId(),
                "UpdatedTestUser",
                "UpdatedTest",
                "UpdatedUser",
                "<EMAIL>"
        );

        // Verify the user was updated successfully
        assertNotNull("Updated user should not be null", updatedUser);
        assertEquals("Username should be updated", "UpdatedTestUser", updatedUser.getUsername());
        assertEquals("First name should be updated", "UpdatedTest", updatedUser.getPreName());
        assertEquals("Last name should be updated", "UpdatedUser", updatedUser.getSurName());
        assertEquals("Email should be updated", "<EMAIL>", updatedUser.getMail());

        // Verify the updates were persisted
        User retrievedUser = userManager.getUser(testUser.getId());
        assertNotNull("Retrieved user should not be null", retrievedUser);
        assertEquals("Retrieved username should match updated username", "UpdatedTestUser", retrievedUser.getUsername());
        assertEquals("Retrieved first name should match updated first name", "UpdatedTest", retrievedUser.getPreName());
        assertEquals("Retrieved last name should match updated last name", "UpdatedUser", retrievedUser.getSurName());
        assertEquals("Retrieved email should match updated email", "<EMAIL>", retrievedUser.getMail());
    }

    /**
     * Test updating only some user profile fields.
     */
    public void testUpdatePartialUserProfile() {
        // Update only the first name and last name
        User updatedUser = userManager.updateUser(
                testUser.getId(),
                null,  // Keep the same username
                "PartialTest",
                "PartialUser",
                null   // Keep the same email
        );

        // Verify the user was updated successfully
        assertNotNull("Updated user should not be null", updatedUser);
        assertEquals("Username should remain the same", testUser.getUsername(), updatedUser.getUsername());
        assertEquals("First name should be updated", "PartialTest", updatedUser.getPreName());
        assertEquals("Last name should be updated", "PartialUser", updatedUser.getSurName());
        assertEquals("Email should remain the same", testUser.getMail(), updatedUser.getMail());

        // Verify the updates were persisted
        User retrievedUser = userManager.getUser(testUser.getId());
        assertNotNull("Retrieved user should not be null", retrievedUser);
        assertEquals("Retrieved username should remain the same", testUser.getUsername(), retrievedUser.getUsername());
        assertEquals("Retrieved first name should be updated", "PartialTest", retrievedUser.getPreName());
        assertEquals("Retrieved last name should be updated", "PartialUser", retrievedUser.getSurName());
        assertEquals("Retrieved email should remain the same", testUser.getMail(), retrievedUser.getMail());
    }

    /**
     * Test resetting a user's password.
     */
    public void testResetPassword() {
        // Original password
        String originalPassword = "Password123!";
        // New password
        String newPassword = "NewPassword456@";

        // Reset the password
        boolean resetSuccess = userManager.resetPassword(testUser.getId(), originalPassword, newPassword);
        assertTrue("Password reset should succeed", resetSuccess);

        // Verify the new password works for authentication
        User authenticatedUser = userManager.authenticate(testUser.getUsername(), newPassword);
        assertNotNull("User should authenticate with new password", authenticatedUser);
        assertEquals("Authenticated user ID should match test user ID", testUser.getId(), authenticatedUser.getId());

        // Verify the old password no longer works
        User failedAuthUser = userManager.authenticate(testUser.getUsername(), originalPassword);
        assertNull("User should not authenticate with old password", failedAuthUser);
    }

    /**
     * Test that password reset fails with incorrect old password.
     */
    public void testResetPasswordWithIncorrectOldPassword() {
        // Incorrect old password
        String incorrectOldPassword = "WrongPassword123!";
        // New password
        String newPassword = "NewPassword456@";

        // Attempt to reset the password with incorrect old password
        boolean resetSuccess = userManager.resetPassword(testUser.getId(), incorrectOldPassword, newPassword);
        assertFalse("Password reset should fail with incorrect old password", resetSuccess);

        // Verify the original password still works
        User authenticatedUser = userManager.authenticate(testUser.getUsername(), "Password123!");
        assertNotNull("User should still authenticate with original password", authenticatedUser);
        assertEquals("Authenticated user ID should match test user ID", testUser.getId(), authenticatedUser.getId());
    }

    /**
     * Test that password reset fails with invalid new password.
     */
    public void testResetPasswordWithInvalidNewPassword() {
        // Original password
        String originalPassword = "Password123!";
        // Invalid new password (too short)
        String invalidNewPassword = "Short1!";

        // Attempt to reset the password with invalid new password
        boolean resetSuccess = userManager.resetPassword(testUser.getId(), originalPassword, invalidNewPassword);
        assertFalse("Password reset should fail with invalid new password", resetSuccess);

        // Verify the original password still works
        User authenticatedUser = userManager.authenticate(testUser.getUsername(), originalPassword);
        assertNotNull("User should still authenticate with original password", authenticatedUser);
        assertEquals("Authenticated user ID should match test user ID", testUser.getId(), authenticatedUser.getId());
    }
}
