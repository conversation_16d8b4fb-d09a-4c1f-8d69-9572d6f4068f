package de.flowhive;

import de.flowhive.project.Project;
import de.flowhive.project.ProjectManager;
import de.flowhive.project.attributes.Sprint;
import de.flowhive.project.attributes.UserStory;
import de.flowhive.user.User;
import de.flowhive.user.UserManager;
import de.flowhive.utils.TestEnvironmentSetup;
import junit.framework.TestCase;

import java.util.Date;
import java.util.List;

/**
 * Test cases for sprint operations.
 */
public class SprintTests extends TestCase {

    private ProjectManager projectManager;
    private UserManager userManager;
    private User testUser;
    private Project testProject;
    private UserStory testUserStory1;
    private UserStory testUserStory2;

    /**
     * Set up the test environment before each test.
     * Creates a test user, a test project, and test user stories.
     */
    @Override
    protected void setUp() throws Exception {
        super.setUp();

        // Set up environment variables for testing
        TestEnvironmentSetup.setupEnvironment();

        // Initialize FlowHive instance
        FlowHive.getInstance();

        // Initialize managers
        projectManager = FlowHive.getInstance().getProjectManager();
        userManager = FlowHive.getInstance().getUserManager();

        // Create a test user
        testUser = userManager.register("SprintTestUser", "Password123!", "Test", "User", "<EMAIL>");
        assertNotNull("Failed to create test user", testUser);

        // Create a test project
        testProject = projectManager.createProject(testUser.getId(), "Sprint Test Project");
        assertNotNull("Failed to create test project", testProject);

        // Create test user stories
        testUserStory1 = projectManager.createUserStory(testProject.getId(), "Sprint Test User Story 1", "User story 1 for sprint tests");
        assertNotNull("Failed to create test user story 1", testUserStory1);

        testUserStory2 = projectManager.createUserStory(testProject.getId(), "Sprint Test User Story 2", "User story 2 for sprint tests");
        assertNotNull("Failed to create test user story 2", testUserStory2);
    }

    /**
     * Clean up the test environment after each test.
     * Deletes the test user stories, test project, and test user.
     */
    @Override
    protected void tearDown() throws Exception {
        // Clean up by deleting the test user stories, project, and user
        if (testUserStory1 != null) {
            projectManager.deleteUserStory(testUserStory1.getId());
        }

        if (testUserStory2 != null) {
            projectManager.deleteUserStory(testUserStory2.getId());
        }

        if (testProject != null) {
            projectManager.deleteProject(testProject.getId());
        }

        if (testUser != null) {
            userManager.deleteUser(testUser.getId());
        }

        super.tearDown();
    }

    /**
     * Test creating a sprint with basic information.
     */
    public void testCreateSprint() {
        // Create a sprint
        Sprint sprint = projectManager.createSprint(
                testProject.getId(),
                "Test Sprint",
                "This is a test sprint description",
                new Date(),
                new Date()
        );

        // Verify the sprint was created successfully
        assertNotNull("Sprint should not be null", sprint);
        assertEquals("Sprint name should match", "Test Sprint", sprint.getName());
        assertEquals("Sprint description should match", "This is a test sprint description", sprint.getDescription());

        // Verify the sprint can be retrieved
        Sprint retrievedSprint = projectManager.getSprint(sprint.getId());
        assertNotNull("Retrieved sprint should not be null", retrievedSprint);
        assertEquals("Retrieved sprint ID should match", sprint.getId(), retrievedSprint.getId());
    }

    /**
     * Test updating a sprint.
     */
    public void testUpdateSprint() {
        // Create a sprint
        Sprint sprint = projectManager.createSprint(
                testProject.getId(),
                "Original Sprint Name",
                "Original sprint description",
                new Date(),
                new Date()
        );
        assertNotNull("Sprint should not be null", sprint);

        // Update the sprint
        sprint.setName("Updated Sprint Name");
        sprint.setDescription("Updated sprint description");

        Sprint updatedSprint = projectManager.updateSprint(sprint.getId(), sprint);
        assertNotNull("Updated sprint should not be null", updatedSprint);

        // Verify the updates were applied
        assertEquals("Sprint name should be updated", "Updated Sprint Name", updatedSprint.getName());
        assertEquals("Sprint description should be updated", "Updated sprint description", updatedSprint.getDescription());
    }

    /**
     * Test deleting a sprint.
     */
    public void testDeleteSprint() {
        // Create a sprint
        Sprint sprint = projectManager.createSprint(
                testProject.getId(),
                "Sprint to Delete",
                "This sprint will be deleted",
                new Date(),
                new Date()
        );
        assertNotNull("Sprint should not be null", sprint);

        // Verify the sprint exists
        Sprint retrievedBeforeDelete = projectManager.getSprint(sprint.getId());
        assertNotNull("Sprint should exist before deletion", retrievedBeforeDelete);

        // Delete the sprint
        projectManager.deleteSprint(sprint.getId());

        // Verify the sprint no longer exists
        Sprint retrievedAfterDelete = projectManager.getSprint(sprint.getId());
        assertNull("Sprint should not exist after deletion", retrievedAfterDelete);
    }

    /**
     * Test retrieving all sprints for a project.
     */
    public void testGetSprintsForProject() {
        // Create multiple sprints
        Sprint sprint1 = projectManager.createSprint(testProject.getId(), "Sprint 1", "Description 1", new Date(), new Date());
        Sprint sprint2 = projectManager.createSprint(testProject.getId(), "Sprint 2", "Description 2", new Date(), new Date());
        Sprint sprint3 = projectManager.createSprint(testProject.getId(), "Sprint 3", "Description 3", new Date(), new Date());

        // Retrieve all sprints for the project
        List<Sprint> sprints = projectManager.getSprintsForProject(testProject.getId());

        // Verify the list contains the created sprints
        assertNotNull("Sprint list should not be null", sprints);
        assertTrue("Sprint list should contain at least 3 items", sprints.size() >= 3);

        // Verify the list contains the specific sprints we created
        boolean foundSprint1 = false;
        boolean foundSprint2 = false;
        boolean foundSprint3 = false;

        for (Sprint sprint : sprints) {
            if (sprint.getId() == sprint1.getId()) foundSprint1 = true;
            if (sprint.getId() == sprint2.getId()) foundSprint2 = true;
            if (sprint.getId() == sprint3.getId()) foundSprint3 = true;
        }

        assertTrue("Sprint list should contain Sprint 1", foundSprint1);
        assertTrue("Sprint list should contain Sprint 2", foundSprint2);
        assertTrue("Sprint list should contain Sprint 3", foundSprint3);
    }

    /**
     * Test adding user stories to a sprint.
     */
    public void testAddUserStoriesToSprint() {
        // Create a sprint
        Sprint sprint = projectManager.createSprint(
                testProject.getId(),
                "User Story Sprint",
                "Sprint for testing user story assignments",
                new Date(),
                new Date()
        );
        assertNotNull("Sprint should not be null", sprint);

        // Add user stories to the sprint
        boolean added1 = projectManager.addUserStoryToSprint(sprint.getId(), testUserStory1.getId());
        boolean added2 = projectManager.addUserStoryToSprint(sprint.getId(), testUserStory2.getId());

        // Verify the user stories were added successfully
        assertTrue("User story 1 should be added to sprint", added1);
        assertTrue("User story 2 should be added to sprint", added2);

        // Retrieve user stories for the sprint
        List<UserStory> userStories = projectManager.getUserStoriesForSprint(sprint.getId());

        // Verify the list contains the added user stories
        assertNotNull("User story list should not be null", userStories);
        assertEquals("User story list should contain 2 items", 2, userStories.size());

        // Verify the list contains the specific user stories we added
        boolean foundUserStory1 = false;
        boolean foundUserStory2 = false;

        for (UserStory userStory : userStories) {
            if (userStory.getId() == testUserStory1.getId()) foundUserStory1 = true;
            if (userStory.getId() == testUserStory2.getId()) foundUserStory2 = true;
        }

        assertTrue("User story list should contain User Story 1", foundUserStory1);
        assertTrue("User story list should contain User Story 2", foundUserStory2);
    }

    /**
     * Test removing user stories from a sprint.
     */
    public void testRemoveUserStoriesFromSprint() {
        // Create a sprint
        Sprint sprint = projectManager.createSprint(
                testProject.getId(),
                "User Story Removal Sprint",
                "Sprint for testing user story removals",
                new Date(),
                new Date()
        );
        assertNotNull("Sprint should not be null", sprint);

        // Add user stories to the sprint
        projectManager.addUserStoryToSprint(sprint.getId(), testUserStory1.getId());
        projectManager.addUserStoryToSprint(sprint.getId(), testUserStory2.getId());

        // Verify both user stories are in the sprint
        List<UserStory> userStoriesBeforeRemoval = projectManager.getUserStoriesForSprint(sprint.getId());
        assertEquals("Sprint should have 2 user stories before removal", 2, userStoriesBeforeRemoval.size());

        // Remove one user story from the sprint
        boolean removed = projectManager.removeUserStoryFromSprint(sprint.getId(), testUserStory1.getId());
        assertTrue("User story 1 should be removed from sprint", removed);

        // Verify only one user story remains in the sprint
        List<UserStory> userStoriesAfterRemoval = projectManager.getUserStoriesForSprint(sprint.getId());
        assertEquals("Sprint should have 1 user story after removal", 1, userStoriesAfterRemoval.size());
        assertEquals("Remaining user story should be User Story 2", testUserStory2.getId(), userStoriesAfterRemoval.get(0).getId());
    }

    /**
     * Test getting the project ID for a sprint.
     */
    public void testGetProjectIdForSprint() {
        // Create a sprint
        Sprint sprint = projectManager.createSprint(
                testProject.getId(),
                "Project ID Test",
                "Testing project ID retrieval",
                new Date(),
                new Date()
        );
        assertNotNull("Sprint should not be null", sprint);

        // Get the project ID for the sprint
        int projectId = projectManager.getProjectIdForSprint(sprint.getId());

        // Verify the project ID matches the test project
        assertEquals("Project ID should match the test project", testProject.getId(), projectId);
    }

    /**
     * Test that getSprint returns a sprint with populated user stories.
     */
    public void testGetSprintWithUserStories() {
        // Create a sprint
        Sprint sprint = projectManager.createSprint(
                testProject.getId(),
                "Sprint with User Stories",
                "Testing getSprint with user stories"
        );
        assertNotNull("Sprint should not be null", sprint);

        // Add user stories to the sprint
        boolean added1 = projectManager.addUserStoryToSprint(sprint.getId(), testUserStory1.getId());
        boolean added2 = projectManager.addUserStoryToSprint(sprint.getId(), testUserStory2.getId());
        assertTrue("User story 1 should be added to sprint", added1);
        assertTrue("User story 2 should be added to sprint", added2);

        // Retrieve the sprint using getSprint method
        Sprint retrievedSprint = projectManager.getSprint(sprint.getId());
        assertNotNull("Retrieved sprint should not be null", retrievedSprint);

        // Verify the sprint has the correct basic information
        assertEquals("Sprint ID should match", sprint.getId(), retrievedSprint.getId());
        assertEquals("Sprint name should match", "Sprint with User Stories", retrievedSprint.getName());
        assertEquals("Sprint description should match", "Testing getSprint with user stories", retrievedSprint.getDescription());

        // Verify the sprint has populated user stories
        List<UserStory> userStories = retrievedSprint.getUserStories();
        assertNotNull("User stories list should not be null", userStories);
        assertEquals("User stories list should contain 2 items", 2, userStories.size());

        // Verify the user stories are the correct ones
        boolean foundUserStory1 = false;
        boolean foundUserStory2 = false;

        for (UserStory userStory : userStories) {
            if (userStory.getId() == testUserStory1.getId()) {
                foundUserStory1 = true;
                assertEquals("User story 1 name should match", testUserStory1.getName(), userStory.getName());
            }
            if (userStory.getId() == testUserStory2.getId()) {
                foundUserStory2 = true;
                assertEquals("User story 2 name should match", testUserStory2.getName(), userStory.getName());
            }
        }

        assertTrue("Should find user story 1 in the sprint", foundUserStory1);
        assertTrue("Should find user story 2 in the sprint", foundUserStory2);
    }
}
