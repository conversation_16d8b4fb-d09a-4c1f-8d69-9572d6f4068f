package de.flowhive.suites;

import de.flowhive.SprintTests;
import de.flowhive.TaskTests;
import de.flowhive.UserProjectTests;
import de.flowhive.UserStoryTests;
import junit.framework.Test;
import junit.framework.TestSuite;

/**
 * Test suite that combines all project management related tests.
 */
public class ProjectManagementTestSuite {

    /**
     * Create a test suite containing all project management tests.
     *
     * @return The test suite
     */
    public static Test suite() {
        TestSuite suite = new TestSuite("Project Management Tests");

        // Add user story tests
        suite.addTestSuite(UserStoryTests.class);

        // Add task tests
        suite.addTestSuite(TaskTests.class);

        // Add existing project tests
        suite.addTestSuite(UserProjectTests.class);

        // Add sprint tests
        suite.addTestSuite(SprintTests.class);

        return suite;
    }
}
