package de.flowhive.suites;

import de.flowhive.UserUpdateTests;
import junit.framework.Test;
import junit.framework.TestSuite;

/**
 * Test suite that combines all user management related tests.
 */
public class UserManagementTestSuite {

    /**
     * Create a test suite containing all user management tests.
     *
     * @return The test suite
     */
    public static Test suite() {
        TestSuite suite = new TestSuite("User Management Tests");

        // Add user update tests
        suite.addTestSuite(UserUpdateTests.class);

        return suite;
    }
}
