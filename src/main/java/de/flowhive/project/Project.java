package de.flowhive.project;

import de.flowhive.project.attributes.Sprint;
import de.flowhive.project.attributes.UserStory;

import java.util.ArrayList;
import java.util.List;

public class Project {

    private int id;
    private String name, standards;
    private int productOwner;
    private List<Integer> members;
    private List<UserStory> backlog; // please replace with UserStory later
    private List<Sprint> sprints; // please replace with Sprint later

    public Project(int id, String name, int productOwner) {
        this.id = id;
        this.name = name;
        this.productOwner = productOwner;

        members = new ArrayList<Integer>();
        backlog = new ArrayList<UserStory>();
        sprints = new ArrayList<Sprint>();
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStandards() {
        return standards;
    }

    public void setStandards(String standards) {
        this.standards = standards;
    }

    public int getProductOwner() {
        return productOwner;
    }

    public List<Integer> getMembers() {
        return members;
    }

    public List<Sprint> getSprints() {
        return sprints;
    }

    public List<UserStory> getBacklog() {
        return backlog;
    }
}
