package de.flowhive.project;

import de.flowhive.project.attributes.Sprint;
import de.flowhive.project.attributes.Task;
import de.flowhive.project.attributes.UserStory;
import de.flowhive.project.factories.SprintFactory;
import de.flowhive.project.factories.TaskFactory;
import de.flowhive.project.factories.UserStoryFactory;
import de.flowhive.storage.ProjectDatabaseImpl;
import de.flowhive.storage.SprintDatabaseImpl;
import de.flowhive.storage.TaskDatabaseImpl;
import de.flowhive.storage.UserStoryDatabaseImpl;
import de.flowhive.utils.HiveLog;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

public class ProjectManager {

    private ProjectDatabaseImpl database;
    private TaskFactory taskFactory;
    private UserStoryFactory userStoryFactory;
    private SprintFactory sprintFactory;

    public ProjectManager() {
        database = new ProjectDatabaseImpl();
        userStoryFactory = new UserStoryFactory();
        sprintFactory = new SprintFactory();
        taskFactory = new TaskFactory();
    }

    /**
     * Creates a new project for the given product owner with the specified name.
     */
    public Project createProject(int userId, String projectName) {
        try {
            return database.createProject(projectName, userId);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error creating project " + e.getSQLState());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Creates a new project for the given product owner with the specified name and standards.
     */
    public Project createProject(int userId, String projectName, String standards) {
        try {
            Project project = database.createProject(projectName, userId);
            if (project != null && standards != null && !standards.isEmpty()) {
                project.setStandards(standards);
                database.updateProject(project);
            }
            return project;
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error creating project " + e.getSQLState());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Configures the members of a project by updating the join table.
     */
    public void manageMembers(int projectId, List<Integer> members) {
        try {
            database.updateProjectMembers(projectId, members);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error updating project members" + e);
            e.printStackTrace();
        }
    }

    /**
     * Retrieves a project by its ID.
     */
    public Project getProject(int projectId) {
        try {
            return database.getProject(projectId);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error fetching project" + e);
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Deletes a project (and its associated members via cascading delete).
     */
    public void deleteProject(int projectId) {
        try {
            database.deleteProject(projectId);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error deleting project" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Updates project details such as name, standards, or product owner.
     * You must supply a fully populated Project object with the desired changes.
     */
    public void updateProject(Project project) {
        try {
            database.updateProject(project);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error updating project" + e.getMessage());
            e.printStackTrace();
        }
    }

    // ===== User Story Management Methods =====

    /**
     * Creates a new user story with basic information.
     *
     * @param projectId The ID of the project this user story belongs to
     * @param name The name of the user story
     * @param description The description of the user story
     * @return The newly created UserStory object or null if creation failed
     */
    public UserStory createUserStory(int projectId, String name, String description) {
        return userStoryFactory.create(projectId, name, description);
    }

    /**
     * Creates a new user story with specified priority.
     *
     * @param projectId The ID of the project this user story belongs to
     * @param name The name of the user story
     * @param description The description of the user story
     * @param priority The priority level of the user story
     * @return The newly created UserStory object or null if creation failed
     */
    public UserStory createUserStory(int projectId, String name, String description, PRIORITY priority) {
        return userStoryFactory.create(projectId, name, description, priority);
    }

    /**
     * Updates an existing user story.
     *
     * @param userStoryId The ID of the user story to update
     * @param updatedUserStory The UserStory object with updated values
     * @return The updated UserStory object or null if update failed
     */
    public UserStory updateUserStory(int userStoryId, UserStory updatedUserStory) {
        return userStoryFactory.update(userStoryId, updatedUserStory);
    }

    /**
     * Deletes a user story.
     *
     * @param userStoryId The ID of the user story to delete
     */
    public void deleteUserStory(int userStoryId) {
        userStoryFactory.drop(userStoryId);
    }

    /**
     * Retrieves a user story by its ID.
     *
     * @param userStoryId The ID of the user story to retrieve
     * @return The UserStory object or null if not found
     */
    public UserStory getUserStory(int userStoryId) {
        return userStoryFactory.getUserStory(userStoryId);
    }

    /**
     * Retrieves all user stories for a specific project.
     *
     * @param projectId The ID of the project
     * @return A list of UserStory objects
     */
    public List<UserStory> getUserStoriesForProject(int projectId) {
        return userStoryFactory.getUserStoriesForProject(projectId);
    }

    /**
     * Gets the project ID for a user story.
     *
     * @param userStoryId The ID of the user story
     * @return The project ID or -1 if not found
     */
    public int getProjectIdForUserStory(int userStoryId) {
        return userStoryFactory.getProjectIdForUserStory(userStoryId);
    }

    // ===== Task Management Methods =====

    /**
     * Creates a new task with basic information.
     *
     * @param userStoryId The ID of the user story this task belongs to
     * @param name The name of the task
     * @param description The description of the task
     * @return The newly created Task object or null if creation failed
     */
    public Task createTask(int userStoryId, String name, String description) {
        return taskFactory.create(userStoryId, name, description);
    }

    /**
     * Creates a new task with specified priority and status.
     *
     * @param userStoryId The ID of the user story this task belongs to
     * @param name The name of the task
     * @param description The description of the task
     * @param priority The priority level of the task
     * @param status The current status of the task
     * @return The newly created Task object or null if creation failed
     */
    public Task createTask(int userStoryId, String name, String description, PRIORITY priority, TASKSTATUS status) {
        return taskFactory.create(userStoryId, name, description, priority, status);
    }

    /**
     * Creates a new task with all details including dates and estimated time.
     *
     * @param userStoryId The ID of the user story this task belongs to
     * @param name The name of the task
     * @param description The description of the task
     * @param priority The priority level of the task
     * @param status The current status of the task
     * @param startDate The start date for the task
     * @param dueDate The due date for the task
     * @param estimatedWorkTime The estimated work time in hours
     * @return The newly created Task object or null if creation failed
     */
    public Task createTask(int userStoryId, String name, String description, PRIORITY priority, TASKSTATUS status,
                           Date startDate, Date dueDate, int estimatedWorkTime) {
        return taskFactory.create(userStoryId, name, description, priority, status, startDate, dueDate, estimatedWorkTime);
    }

    /**
     * Updates an existing task.
     *
     * @param taskId The ID of the task to update
     * @param updatedTask The Task object with updated values
     * @return The updated Task object or null if update failed
     */
    public Task updateTask(int taskId, Task updatedTask) {
        return taskFactory.update(taskId, updatedTask);
    }

    /**
     * Deletes a task.
     *
     * @param taskId The ID of the task to delete
     */
    public void deleteTask(int taskId) {
        taskFactory.drop(taskId);
    }

    /**
     * Updates the assignees for a task.
     *
     * @param taskId The ID of the task
     * @param assignees List of user IDs to assign to the task
     * @return True if the update was successful, false otherwise
     */
    public boolean updateTaskAssignees(int taskId, List<Integer> assignees) {
        return taskFactory.updateAssignees(taskId, assignees);
    }

    /**
     * Retrieves a task by its ID.
     *
     * @param taskId The ID of the task to retrieve
     * @return The Task object or null if not found
     */
    public Task getTask(int taskId) {
        return taskFactory.getTask(taskId);
    }

    /**
     * Retrieves all tasks for a specific user story.
     *
     * @param userStoryId The ID of the user story
     * @return A list of Task objects
     */
    public List<Task> getTasksForUserStory(int userStoryId) {
        return taskFactory.getTasksForUserStory(userStoryId);
    }

    // ===== Sprint Management Methods =====

    /**
     * Creates a new sprint with basic information.
     *
     * @param projectId The ID of the project this sprint belongs to
     * @param name The name of the sprint
     * @param description The description of the sprint
     * @return The newly created Sprint object or null if creation failed
     */
    public Sprint createSprint(int projectId, String name, String description) {
        return sprintFactory.create(projectId, name, description);
    }

    /**
     * Creates a new sprint with start and due dates.
     *
     * @param projectId The ID of the project this sprint belongs to
     * @param name The name of the sprint
     * @param description The description of the sprint
     * @param start The start date of the sprint
     * @param due The due date of the sprint
     * @return The newly created Sprint object or null if creation failed
     */
    public Sprint createSprint(int projectId, String name, String description, Date start, Date due) {
        return sprintFactory.create(projectId, name, description, start, due);
    }

    /**
     * Updates an existing sprint.
     *
     * @param sprintId The ID of the sprint to update
     * @param updatedSprint The Sprint object with updated values
     * @return The updated Sprint object or null if update failed
     */
    public Sprint updateSprint(int sprintId, Sprint updatedSprint) {
        return sprintFactory.update(sprintId, updatedSprint);
    }

    /**
     * Deletes a sprint.
     *
     * @param sprintId The ID of the sprint to delete
     */
    public void deleteSprint(int sprintId) {
        sprintFactory.drop(sprintId);
    }

    /**
     * Retrieves a sprint by its ID.
     *
     * @param sprintId The ID of the sprint to retrieve
     * @return The Sprint object or null if not found
     */
    public Sprint getSprint(int sprintId) {
        return sprintFactory.getSprint(sprintId);
    }

    /**
     * Retrieves all sprints for a specific project.
     *
     * @param projectId The ID of the project
     * @return A list of Sprint objects
     */
    public List<Sprint> getSprintsForProject(int projectId) {
        return sprintFactory.getSprintsForProject(projectId);
    }

    /**
     * Gets the project ID for a sprint.
     *
     * @param sprintId The ID of the sprint
     * @return The project ID or -1 if not found
     */
    public int getProjectIdForSprint(int sprintId) {
        return sprintFactory.getProjectIdForSprint(sprintId);
    }

    /**
     * Adds a user story to a sprint.
     *
     * @param sprintId The ID of the sprint
     * @param userStoryId The ID of the user story
     * @return True if successful, false otherwise
     */
    public boolean addUserStoryToSprint(int sprintId, int userStoryId) {
        return sprintFactory.addUserStoryToSprint(sprintId, userStoryId);
    }

    /**
     * Removes a user story from a sprint.
     *
     * @param sprintId The ID of the sprint
     * @param userStoryId The ID of the user story
     * @return True if successful, false otherwise
     */
    public boolean removeUserStoryFromSprint(int sprintId, int userStoryId) {
        return sprintFactory.removeUserStoryFromSprint(sprintId, userStoryId);
    }

    /**
     * Gets all user stories for a sprint.
     *
     * @param sprintId The ID of the sprint
     * @return A list of UserStory objects
     */
    public List<UserStory> getUserStoriesForSprint(int sprintId) {
        return sprintFactory.getUserStoriesForSprint(sprintId);
    }
}
