package de.flowhive.project.factories;

import de.flowhive.FlowHive;
import de.flowhive.project.PRIORITY;
import de.flowhive.project.attributes.UserStory;
import de.flowhive.storage.UserStoryDatabaseImpl;
import de.flowhive.utils.HiveLog;

import java.sql.SQLException;
import java.util.List;

public class UserStoryFactory implements ProjectAttributeFactory<UserStory> {

    private UserStoryDatabaseImpl userStoryDatabase;

    public UserStoryFactory() {
        this.userStoryDatabase = new UserStoryDatabaseImpl();
    }

    @Override
    public Class<UserStory> getAttributeClass() {
        return UserStory.class;
    }

    @Override
    public UserStory create(int projectId, String name, String description) {
        try {
            // Create a user story with default medium priority
            return userStoryDatabase.createUserStory(projectId, name, description, PRIORITY.MEDIUM);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error creating user story: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Creates a user story with specified priority.
     *
     * @param projectId The ID of the project this user story belongs to
     * @param name The name of the user story
     * @param description The description of the user story
     * @param priority The priority level of the user story
     * @return The newly created UserStory object or null if creation failed
     */
    public UserStory create(int projectId, String name, String description, PRIORITY priority) {
        try {
            return userStoryDatabase.createUserStory(projectId, name, description, priority);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error creating user story: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public void drop(int userStoryId) {
        try {
            userStoryDatabase.deleteUserStory(userStoryId);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error deleting user story: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public UserStory update(int userStoryId, UserStory updatedUserStory) {
        try {
            // Make sure the user story ID is set correctly
            if (updatedUserStory.getId() != userStoryId) {
                HiveLog.error(getClass(), "User story ID mismatch during update");
                return null;
            }

            userStoryDatabase.updateUserStory(updatedUserStory);
            return userStoryDatabase.getUserStory(userStoryId);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error updating user story: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Retrieves a user story by its ID.
     *
     * @param userStoryId The ID of the user story to retrieve
     * @return The UserStory object or null if not found
     */
    public UserStory getUserStory(int userStoryId) {
        try {
            return userStoryDatabase.getUserStory(userStoryId);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error retrieving user story: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Retrieves all user stories for a specific project.
     *
     * @param projectId The ID of the project
     * @return A list of UserStory objects
     */
    public List<UserStory> getUserStoriesForProject(int projectId) {
        try {
            return userStoryDatabase.getUserStoriesForProject(projectId);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error retrieving user stories for project: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Gets the project ID for a user story.
     *
     * @param userStoryId The ID of the user story
     * @return The project ID or -1 if not found
     */
    public int getProjectIdForUserStory(int userStoryId) {
        try {
            return userStoryDatabase.getProjectIdForUserStory(userStoryId);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error retrieving project ID for user story: " + e.getMessage());
            e.printStackTrace();
            return -1;
        }
    }
}
