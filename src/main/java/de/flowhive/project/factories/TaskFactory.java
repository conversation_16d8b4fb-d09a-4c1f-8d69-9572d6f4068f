package de.flowhive.project.factories;

import de.flowhive.FlowHive;
import de.flowhive.project.PRIORITY;
import de.flowhive.project.TASKSTATUS;
import de.flowhive.project.attributes.Task;
import de.flowhive.storage.TaskDatabaseImpl;
import de.flowhive.utils.HiveLog;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

public class TaskFactory implements ProjectAttributeFactory<Task> {

    private final TaskDatabaseImpl taskDatabase;

    public TaskFactory() {
        this.taskDatabase = new TaskDatabaseImpl();
    }

    @Override
    public Class<Task> getAttributeClass() {
        return Task.class;
    }

    @Override
    public Task create(int userStoryId, String name, String description) {
        try {
            // Create a basic task with default priority and status
            return taskDatabase.createTask(userStoryId, name, description, PRIORITY.MEDIUM, TASKSTATUS.NOT_STARTED);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error creating task: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Creates a task with all details specified.
     *
     * @param userStoryId The ID of the user story this task belongs to
     * @param name The name of the task
     * @param description The description of the task
     * @param priority The priority level of the task
     * @param status The current status of the task
     * @return The newly created Task object or null if creation failed
     */
    public Task create(int userStoryId, String name, String description, PRIORITY priority, TASKSTATUS status) {
        try {
            return taskDatabase.createTask(userStoryId, name, description, priority, status);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error creating task: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Creates a task with all details including dates and estimated time.
     *
     * @param userStoryId The ID of the user story this task belongs to
     * @param name The name of the task
     * @param description The description of the task
     * @param priority The priority level of the task
     * @param status The current status of the task
     * @param startDate The start date for the task
     * @param dueDate The due date for the task
     * @param estimatedWorkTime The estimated work time in hours
     * @return The newly created Task object or null if creation failed
     */
    public Task create(int userStoryId, String name, String description, PRIORITY priority, TASKSTATUS status,
                      Date startDate, Date dueDate, int estimatedWorkTime) {
        try {
            return taskDatabase.createTask(userStoryId, name, description, priority, status,
                                         new java.sql.Date(startDate.getTime()),
                                         new java.sql.Date(dueDate.getTime()),
                                         estimatedWorkTime);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error creating task: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public void drop(int taskId) {
        try {
            taskDatabase.deleteTask(taskId);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error deleting task: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public Task update(int taskId, Task updatedTask) {
        try {
            // Make sure the task ID is set correctly
            if (updatedTask.getId() != taskId) {
                HiveLog.error(getClass(), "Task ID mismatch during update");
                return null;
            }

            taskDatabase.updateTask(updatedTask);
            return taskDatabase.getTask(taskId);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error updating task: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Updates the assignees for a task.
     *
     * @param taskId The ID of the task
     * @param assignees List of user IDs to assign to the task
     * @return True if the update was successful, false otherwise
     */
    public boolean updateAssignees(int taskId, List<Integer> assignees) {
        try {
            taskDatabase.updateTaskAssignees(taskId, assignees);
            return true;
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error updating task assignees: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Retrieves a task by its ID.
     *
     * @param taskId The ID of the task to retrieve
     * @return The Task object or null if not found
     */
    public Task getTask(int taskId) {
        try {
            return taskDatabase.getTask(taskId);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error retrieving task: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Retrieves all tasks for a specific user story.
     *
     * @param userStoryId The ID of the user story
     * @return A list of Task objects
     */
    public List<Task> getTasksForUserStory(int userStoryId) {
        try {
            return taskDatabase.getTasksForUserStory(userStoryId);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error retrieving tasks for user story: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
}
