package de.flowhive.project.factories;

import de.flowhive.project.attributes.Sprint;
import de.flowhive.project.attributes.UserStory;
import de.flowhive.storage.SprintDatabaseImpl;
import de.flowhive.storage.UserStoryDatabaseImpl;
import de.flowhive.utils.HiveLog;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SprintFactory implements ProjectAttributeFactory<Sprint> {

    private final SprintDatabaseImpl sprintDatabase;
    private final UserStoryDatabaseImpl userStoryDatabase;

    public SprintFactory() {
        this.userStoryDatabase = new UserStoryDatabaseImpl();
        this.sprintDatabase = new SprintDatabaseImpl(userStoryDatabase);
    }

    @Override
    public Class<Sprint> getAttributeClass() {
        return Sprint.class;
    }

    @Override
    public Sprint create(int projectId, String name, String description) {
        try {
            return sprintDatabase.createSprint(projectId, name, description);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error creating sprint: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    public Sprint create(int projectId, String name, String description, Date start, Date due) {
        try {
            return sprintDatabase.createSprint(projectId, name, description, new java.sql.Date(start.getTime()), new java.sql.Date(due.getTime()));
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error creating sprint: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public void drop(int sprintId) {
        try {
            sprintDatabase.deleteSprint(sprintId);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error deleting sprint: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public Sprint update(int sprintId, Sprint updatedSprint) {
        try {
            // Make sure the sprint ID is set correctly
            if (updatedSprint.getId() != sprintId) {
                HiveLog.error(getClass(), "Sprint ID mismatch during update");
                return null;
            }

            sprintDatabase.updateSprint(updatedSprint);
            return sprintDatabase.getSprint(sprintId);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error updating sprint: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Retrieves a sprint by its ID.
     *
     * @param sprintId The ID of the sprint to retrieve
     * @return The Sprint object or null if not found
     */
    public Sprint getSprint(int sprintId) {
        try {
            return sprintDatabase.getSprint(sprintId);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error retrieving sprint: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Retrieves all sprints for a specific project.
     *
     * @param projectId The ID of the project
     * @return A list of Sprint objects
     */
    public List<Sprint> getSprintsForProject(int projectId) {
        try {
            return sprintDatabase.getSprintsForProject(projectId);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error retrieving sprints for project: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * Gets the project ID for a sprint.
     *
     * @param sprintId The ID of the sprint
     * @return The project ID or -1 if not found
     */
    public int getProjectIdForSprint(int sprintId) {
        try {
            return sprintDatabase.getProjectIdForSprint(sprintId);
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error getting project ID for sprint: " + e.getMessage());
            e.printStackTrace();
            return -1;
        }
    }

    /**
     * Adds a user story to a sprint.
     *
     * @param sprintId The ID of the sprint
     * @param userStoryId The ID of the user story
     * @return True if successful, false otherwise
     */
    public boolean addUserStoryToSprint(int sprintId, int userStoryId) {
        try {
            sprintDatabase.addUserStoryToSprint(sprintId, userStoryId);
            return true;
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error adding user story to sprint: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Removes a user story from a sprint.
     *
     * @param sprintId The ID of the sprint
     * @param userStoryId The ID of the user story
     * @return True if successful, false otherwise
     */
    public boolean removeUserStoryFromSprint(int sprintId, int userStoryId) {
        try {
            sprintDatabase.removeUserStoryFromSprint(sprintId, userStoryId);
            return true;
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error removing user story from sprint: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Gets all user stories for a sprint.
     *
     * @param sprintId The ID of the sprint
     * @return A list of UserStory objects
     */
    public List<UserStory> getUserStoriesForSprint(int sprintId) {
        try {
            List<Integer> userStoryIds = sprintDatabase.getUserStoryIdsForSprint(sprintId);
            List<UserStory> userStories = new ArrayList<>();

            for (Integer userStoryId : userStoryIds) {
                UserStory userStory = userStoryDatabase.getUserStory(userStoryId);
                if (userStory != null) {
                    userStories.add(userStory);
                }
            }

            return userStories;
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Error getting user stories for sprint: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }
}
