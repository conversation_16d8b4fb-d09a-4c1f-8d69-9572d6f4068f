package de.flowhive.project.attributes;

import de.flowhive.project.PRIORITY;

import java.util.ArrayList;
import java.util.List;

public class UserStory implements IProjectAttribute {

    private final int id;
    private final List<Task> tasks;
    private String name, description;
    private PRIORITY priority;

    public UserStory(int id) {
        this.id = id;
        this.tasks = new ArrayList<>();
    }

    public List<Task> getTasks() {
        return tasks;
    }

    @Override
    public int getId() {
        return id;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public PRIORITY getPriority() {
        return priority;
    }

    public void setPriority(PRIORITY priority) {
        this.priority = priority;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }
}
