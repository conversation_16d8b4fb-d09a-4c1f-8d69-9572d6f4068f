package de.flowhive.project.attributes;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class Sprint implements IProjectAttribute {

    private final int id;
    private final List<UserStory> userStories;
    private Date startDate, dueDate;
    private String name, description;

    public Sprint(int id) {
        this.id = id;
        userStories = new ArrayList<UserStory>();
    }

    public List<UserStory> getUserStories() {
        return userStories;
    }

    @Override
    public int getId() {
        return id;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    public void setStartDate(Date starDate) {
        this.startDate = starDate;
    }

    public Date getStartDate() {
        return startDate;
    }

    public Date getDueDate() {
        return dueDate;
    }

    public void setDueDate(Date dueDate) {
        this.dueDate = dueDate;
    }
}
