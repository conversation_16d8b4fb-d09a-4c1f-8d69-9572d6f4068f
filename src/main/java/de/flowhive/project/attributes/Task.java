package de.flowhive.project.attributes;

import de.flowhive.project.PRIORITY;
import de.flowhive.project.TASKSTATUS;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class Task implements IProjectAttribute {

    private final int id;
    private final List<Integer> assignedTo;
    private String name, description;
    private PRIORITY priority;
    private Date starDate, dueDate;
    private int estimatedWorkTime, actualWorkTime;
    private TASKSTATUS status;

    public Task(int id) {
        this.id = id;
        this.assignedTo = new ArrayList<>();
    }

    public List<Integer> getAssignedTo() {
        return assignedTo;
    }

    public PRIORITY getPriority() {
        return priority;
    }

    public void setPriority(PRIORITY priority) {
        this.priority = priority;
    }

    public TASKSTATUS getStatus() {
        return status;
    }

    public void setStatus(TASKSTATUS status) {
        this.status = status;
    }

    public Date getStarDate() {
        return starDate;
    }

    public void setStarDate(Date starDate) {
        this.starDate = starDate;
    }

    public Date getDueDate() {
        return dueDate;
    }

    public void setDueDate(Date dueDate) {
        this.dueDate = dueDate;
    }

    public int getEstimatedWorkTime() {
        return estimatedWorkTime;
    }

    public void setEstimatedWorkTime(int estimatedWorkTime) {
        this.estimatedWorkTime = estimatedWorkTime;
    }

    public int getActualWorkTime() {
        return actualWorkTime;
    }

    public void setActualWorkTime(int actualWorkTime) {
        this.actualWorkTime = actualWorkTime;
    }

    @Override
    public int getId() {
        return id;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }
}
