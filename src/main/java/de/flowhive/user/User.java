package de.flowhive.user;

import de.flowhive.notifications.Notification;

import java.util.List;
import java.util.Map;

public class User {

    private int id;
    private String username, preName, surName, mail, password;
    private Map<Integer, ROLE> projects;
    private List<Notification> inbox;

    public User(int id, String username, String preName, String surName, String mail, String password, Map<Integer, ROLE> projects, List<Notification> inbox) {
        this.id = id;
        this.username = username;
        this.preName = preName;
        this.surName = surName;
        this.mail = mail;
        this.password = password;
        this.projects = projects;
        this.inbox = inbox;
    }

    public User(int id, String username) {
        this.id = id;
        this.username = username;
    }

    public int getId() {
        return id;
    }

    public String getUsername() {
        return username;
    }

    public String getPreName() {
        return preName;
    }

    public void setPreName(String preName) {
        this.preName = preName;
    }

    public String getSurName() {
        return surName;
    }

    public void setSurName(String surName) {
        this.surName = surName;
    }

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    public String getPassword() {
        return password;
    }

    public Map<Integer, ROLE> getProjects() {
        return projects;
    }

    public List<Notification> getInbox() {
        return inbox;
    }
}
