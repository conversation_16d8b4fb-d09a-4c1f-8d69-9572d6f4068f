package de.flowhive.user;

import com.fasterxml.jackson.databind.*;
import de.flowhive.storage.UserDatabaseImpl;
import de.flowhive.utils.HiveLog;
import io.javalin.http.util.JsonEscapeUtil;

import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;

import java.security.MessageDigest;
import java.util.Base64;
import java.nio.charset.StandardCharsets;
import java.util.List;

public class UserManager {

    private UserDatabaseImpl database;

    public UserManager() {
        database = new UserDatabaseImpl();
    }


    public String hashPassword(String password) {
        try {
            return Base64.getEncoder().encodeToString(
                    MessageDigest.getInstance("SHA-256").digest(password.getBytes(StandardCharsets.UTF_8)));
        } catch (NoSuchAlgorithmException e) {
            HiveLog.log(getClass(), e.getMessage());
            return null;
        }
    }

    private boolean validateMail(String mail) {
        String mailPattern = "^(?=.{1,64}@)[A-Za-z0-9_-]+(\\.[A-Za-z0-9_-]+)*@"
                + "[^-][A-Za-z0-9-]+(\\.[A-Za-z0-9-]+)*(\\.[A-Za-z]{2,})$";
        return !mail.matches(mailPattern);
    }

    private boolean validatePassword(String password) {
        String passwordPattern = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[.,@$#!%+_*?&^(){}\\[\\]/=<>:;|~\\-])[A-Za-z\\d.,@$#!%+_*?&^(){}\\[\\]/=<>:;|~\\-]{8,64}$";
        return !(password != null && password.matches(passwordPattern));
    }

    private boolean validateUsername(String username) {
        String usernamePattern = "^[a-zA-Z0-9 ._\\-]+$";
        //database.uniqueUsername(username) &&
        return !(username.matches(usernamePattern) && username.length() <= 64);
    }

    private boolean validateName(String name) {
        String namePattern = "^[a-zA-Z]+$";
        return !(name.matches(namePattern) && name.length() <= 64);
    }

    private boolean validateData(String uName, String preName, String surName, String mail) {
        if (validateMail(mail)) {
            HiveLog.debug(getClass(), "E-Mail address is invalid");
            return true;
        }
        if (validateUsername(uName)) {
            HiveLog.debug(getClass(), "Username is invalid");
            return true;
        }
        if (validateName(preName)) {
            HiveLog.debug(getClass(), "Prename is invalid");
            return true;
        }
        if (validateName(surName)) {
            HiveLog.debug(getClass(), "Surname is invalid");
            return true;
        }
        return false;
    }

    public User authenticate(String username, String password) {
        String hashedPswd = hashPassword(password);
        return database.authUser(username, hashedPswd);
    }

    public User register(String uName, String password, String preName, String surName, String mail) {
        if (validateData(uName, preName, surName, mail)) {
            return null;
        }
        if (validatePassword(password)) {
            HiveLog.debug(getClass(), "Password is invalid");
            return null;
        }
        String hashedPswd = hashPassword(password);
        // add new user in db and return it
        int new_id = database.addUser(uName, preName, surName, mail, hashedPswd);
        if (new_id != -1) {
            return new User(new_id, uName, preName, surName, mail, hashedPswd, new HashMap<>(), new ArrayList<>());
        }
        return null;
    }

    public User updateUser(int id, String uName, String preName, String surName, String mail) {
        User u = database.getUser(id);
        if (uName == null) {
            uName = u.getUsername();
        }
        if (preName == null) {
            preName = u.getPreName();
        }
        if (surName == null) {
            surName = u.getSurName();
        }
        if (mail == null) {
            mail = u.getMail();
        }
        if (validateData(uName, preName, surName, mail)) {
            return null;
        }
        if (database.updateUser(id, uName, preName, surName, mail)) {
            return new User(id, uName, preName, surName, mail, mail, new HashMap<>(), new ArrayList<>());
        }
        return null;
    }

    public User getUser(int id) {
        return database.getUser(id);
    }

    public List<User> getUsers(List<Integer> userIds) {
        List<User> users = new ArrayList<>();
        for (Integer id : userIds) {
            users.add(getUser(id));
        }
        return users;
    }

    /**
     * Gets a user by their username.
     *
     * @param username The username to search for
     * @return The User object if found, null otherwise
     */
    public User getUserByUsername(String username) {
        return database.getUserByUsername(username);
    }

    public boolean resetPassword(int id, String oldPassword, String newPassword) {
        if (validatePassword(newPassword)) {
            HiveLog.debug(getClass(), "Password is invalid");
            return false;
        }
        String newHashedPswd = hashPassword(newPassword);
        String oldHashedPswd = hashPassword(oldPassword);
        String userPassword = database.getPassword(id);
        if (oldHashedPswd.equals(userPassword)) {
            return database.updatePassword(id, newHashedPswd);
        } else {
            HiveLog.log(getClass(), "New and old passwords do not match");
            return false;
        }
    }

    public boolean deleteUser(int id) {
        return database.deleteUser(id);
    }

    public JsonNode getInbox(int userId){
        return database.getInbox(userId);
    }

    public void updateInbox(int userId, String message){
        database.updateInbox(userId, message);
    }

}
