package de.flowhive.storage;

import de.flowhive.project.PRIORITY;
import de.flowhive.project.attributes.Task;
import de.flowhive.project.attributes.UserStory;
import de.flowhive.utils.HiveLog;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

public class UserStoryDatabaseImpl extends DatabaseAdapter {

    public UserStoryDatabaseImpl() {
        super("UserStory");

        // create table if not exists
        try {
            queryWithoutResults("CREATE TABLE IF NOT EXISTS \"" + tableName + "\" (" +
                    " id SERIAL PRIMARY KEY," +
                    " project_id INT," +
                    " name VARCHAR(255)," +
                    " description TEXT," +
                    " priority SMALLINT," +
                    " FOREIGN KEY (project_id) REFERENCES \"Project\"(id) ON DELETE CASCADE" +
                    ");");
            HiveLog.debug(getClass(), "Created UserStory table if not exists");
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Failed to create UserStory table!");
            throw new RuntimeException(e);
        }
    }

    /**
     * Creates a new user story in the database and returns the generated user story ID.
     * @param projectId The ID of the project this user story belongs to
     * @param name The name of the user story
     * @param description The description of the user story
     * @param priority The priority level of the user story
     * @return The newly created UserStory object with its ID
     * @throws SQLException If there's an error during database operations
     */
    public UserStory createUserStory(int projectId, String name, String description, PRIORITY priority) throws SQLException {
        String sql = "INSERT INTO \"" + tableName + "\" (project_id, name, description, priority) VALUES (?, ?, ?, ?) RETURNING id;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, projectId);
            stmt.setString(2, name);
            stmt.setString(3, description);
            stmt.setInt(4, priority.ordinal());

            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                int userStoryId = rs.getInt("id");
                UserStory userStory = new UserStory(userStoryId);
                userStory.setName(name);
                userStory.setDescription(description);
                userStory.setPriority(priority);
                return userStory;
            }
            throw new SQLException("Creating user story failed, no ID obtained.");
        }
    }

    /**
     * Retrieves a user story by its ID.
     * @param userStoryId The ID of the user story to retrieve
     * @return The UserStory object or null if not found
     * @throws SQLException If there's an error during database operations
     */
    public UserStory getUserStory(int userStoryId) throws SQLException {
        String sql = "SELECT * FROM \"" + tableName + "\" WHERE id = ?;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, userStoryId);
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                UserStory userStory = new UserStory(rs.getInt("id"));
                userStory.setName(rs.getString("name"));
                userStory.setDescription(rs.getString("description"));

                int priorityOrdinal = rs.getInt("priority");
                userStory.setPriority(PRIORITY.values()[priorityOrdinal]);

                // Load tasks for this user story (if needed)
                // This could be implemented by a TaskDatabaseImpl method

                return userStory;
            }
            return null;
        }
    }

    /**
     * Retrieves all user stories for a specific project.
     * @param projectId The ID of the project
     * @return A list of UserStory objects
     * @throws SQLException If there's an error during database operations
     */
    public List<UserStory> getUserStoriesForProject(int projectId) throws SQLException {
        List<UserStory> userStories = new ArrayList<>();
        String sql = "SELECT id FROM \"" + tableName + "\" WHERE project_id = ?;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, projectId);
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                int userStoryId = rs.getInt("id");
                UserStory userStory = getUserStory(userStoryId);
                if (userStory != null) {
                    userStories.add(userStory);
                }
            }
        }
        return userStories;
    }

    /**
     * Updates an existing user story in the database.
     * @param userStory The UserStory object with updated values
     * @throws SQLException If there's an error during database operations
     */
    public void updateUserStory(UserStory userStory) throws SQLException {
        String sql = "UPDATE \"" + tableName + "\" SET name = ?, description = ?, priority = ? WHERE id = ?;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, userStory.getName());
            stmt.setString(2, userStory.getDescription());
            stmt.setInt(3, userStory.getPriority().ordinal());
            stmt.setInt(4, userStory.getId());

            stmt.executeUpdate();
        }
    }

    /**
     * Deletes a user story from the database.
     * @param userStoryId The ID of the user story to delete
     * @throws SQLException If there's an error during database operations
     */
    public void deleteUserStory(int userStoryId) throws SQLException {
        String sql = "DELETE FROM \"" + tableName + "\" WHERE id = ?;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, userStoryId);
            stmt.executeUpdate();
        }
    }

    /**
     * Gets the project ID for a user story.
     * @param userStoryId The ID of the user story
     * @return The project ID or -1 if not found
     * @throws SQLException If there's an error during database operations
     */
    public int getProjectIdForUserStory(int userStoryId) throws SQLException {
        String sql = "SELECT project_id FROM \"" + tableName + "\" WHERE id = ?;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, userStoryId);
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getInt("project_id");
            }
            return -1;
        }
    }
}
