package de.flowhive.storage;

import de.flowhive.utils.HiveLog;
import org.jetbrains.annotations.Nullable;

import java.sql.*;

public class DatabaseAdapter {

    protected String postgresUri;
    protected String tableName;
    protected static Connection connection;

    public DatabaseAdapter(String tableName) {
        this.tableName = tableName;

        // create connection to database if doesn't exist already
        if (connection == null) {
            String url = getEnvOrProperty("POSTGRESQL_URI");
            String user = getEnvOrProperty("POSTGRES_USER");
            String password = getEnvOrProperty("POSTGRES_PASSWORD");

            try {
                connection = DriverManager.getConnection(url, user, password);
                HiveLog.debug(getClass(), "Database connection established!");
            } catch (SQLException e) {
                HiveLog.error(getClass(), "Could not connect to database");
                throw new RuntimeException(e);
            }

        }
    }

    protected ResultSet queryWithResults(String query) throws SQLException {
        Statement statement = connection.createStatement(ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_UPDATABLE);
        return statement.executeQuery(query);
    }

    protected boolean queryWithoutResults(String query) throws SQLException {
        Statement statement = connection.createStatement();
        return statement.execute(query);
    }

    public static Connection getConnection() {
        return connection;
    }

    /**
     * Gets an environment variable or system property.
     * First checks environment variables, then falls back to system properties.
     *
     * @param name The name of the variable/property
     * @return The value or null if not found
     */
    private String getEnvOrProperty(String name) {
        String value = System.getenv(name);
        if (value == null) {
            value = System.getProperty(name);
        }
        return value;
    }
    // there will be more methods in the future probably
}
