package de.flowhive.storage;

import de.flowhive.project.Project;
import de.flowhive.utils.HiveLog;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class ProjectDatabaseImpl extends DatabaseAdapter {

    public ProjectDatabaseImpl() {
        super("Project");

        // create table if not exists
        try {
            queryWithoutResults("CREATE TABLE IF NOT EXISTS \"" + tableName + "\" (" +
                    " id SERIAL PRIMARY KEY," +
                    " name VARCHAR(255)," +
                    " standards TEXT," +
                    " product_owner INT," +
                    " FOREIGN KEY (product_owner) REFERENCES \"User\"(id) ON DELETE CASCADE" +
                    ");");

            queryWithoutResults("CREATE TABLE IF NOT EXISTS \"Project_Member\" (" +
                    " project_id INT," +
                    " user_id INT," +
                    " PRIMARY KEY (project_id, user_id)," +
                    " FOREIGN KEY (project_id) REFERENCES \"Project\"(id) ON DELETE CASCADE," +
                    " FOREIGN KEY (user_id) REFERENCES \"User\"(id) ON DELETE CASCADE" +
                    ");\n");
            HiveLog.debug(getClass(), "Created Project table if not exists");
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Failed to create Project table!");
            throw new RuntimeException(e);
        }
    }

    /**
     * Inserts a new project and returns the generated project ID.
     */
    public Project createProject(String name, int productOwner) throws SQLException {
        String sql = "INSERT INTO \"" + tableName + "\" (name, product_owner) VALUES (?, ?) RETURNING id;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, name);
            stmt.setInt(2, productOwner);
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return new Project(rs.getInt("id"), name, productOwner);
            }
            throw new SQLException("Creating project failed, no ID obtained.");
        }
    }

    /**
     * Retrieves a Project by its ID. Also loads its members.
     */
    public Project getProject(int projectId) throws SQLException {
        String sql = "SELECT * FROM \"" + tableName + "\" WHERE id = ?;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, projectId);
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                Project project = new Project(rs.getInt("id"), rs.getString("name"), rs.getInt("product_owner"));
                project.setName(rs.getString("name"));
                project.setStandards(rs.getString("standards"));
                project.getMembers().addAll(getProjectMembers(projectId));
                return project;
            }
            return null;
        }
    }

    /**
     * Retrieves the list of member user IDs for the given project.
     */
    public List<Integer> getProjectMembers(int projectId) throws SQLException {
        List<Integer> members = new ArrayList<>();
        String sql = "SELECT user_id FROM \"Project_Member\" WHERE project_id = ?;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, projectId);
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                members.add(rs.getInt("user_id"));
            }
        }
        return members;
    }

    /**
     * Updates the project record in the database.
     */
    public void updateProject(Project project) throws SQLException {
        String sql = "UPDATE \"" + tableName + "\" SET name = ?, standards = ?, product_owner = ? WHERE id = ?;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, project.getName());
            stmt.setString(2, project.getStandards());
            stmt.setInt(3, project.getProductOwner());
            stmt.setInt(4, project.getId());
            stmt.executeUpdate();
        }
    }

    /**
     * Deletes the project record from the database.
     * (Note: The foreign keys on Project_Member will automatically remove associated rows.)
     */
    public void deleteProject(int projectId) throws SQLException {
        String sql = "DELETE FROM \"" + tableName + "\" WHERE id = ?;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, projectId);
            stmt.executeUpdate();
        }
    }

    /**
     * Updates the members of a project. The implementation clears the current list and inserts the new one.
     */
    public void updateProjectMembers(int projectId, List<Integer> members) throws SQLException {
        // Delete all existing members for this project
        String deleteSQL = "DELETE FROM \"Project_Member\" WHERE project_id = ?;";
        try (PreparedStatement stmt = connection.prepareStatement(deleteSQL)) {
            stmt.setInt(1, projectId);
            stmt.executeUpdate();
        }

        // Insert new members (if any)
        if (members != null && !members.isEmpty()) {
            String insertSQL = "INSERT INTO \"Project_Member\" (project_id, user_id) VALUES (?, ?);";
            try (PreparedStatement stmt = connection.prepareStatement(insertSQL)) {
                for (Integer userId : members) {
                    stmt.setInt(1, projectId);
                    stmt.setInt(2, userId);
                    stmt.addBatch();
                }
                stmt.executeBatch();
            }
        }
    }

}
