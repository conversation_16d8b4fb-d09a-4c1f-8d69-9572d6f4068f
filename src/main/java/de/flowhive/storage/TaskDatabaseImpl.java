package de.flowhive.storage;

import de.flowhive.project.PRIORITY;
import de.flowhive.project.TASKSTATUS;
import de.flowhive.project.attributes.Task;
import de.flowhive.utils.HiveLog;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

public class TaskDatabaseImpl extends DatabaseAdapter {

    public TaskDatabaseImpl() {
        super("Task");

        // create table if not exists
        try {
            queryWithoutResults("CREATE TABLE IF NOT EXISTS \"" + tableName + "\" (" +
                    " id SERIAL PRIMARY KEY, " +
                    " user_story_id INT, " +
                    " FOREIGN KEY (user_story_id) REFERENCES \"UserStory\"(id) ON DELETE CASCADE, " +
                    " name VARCHAR(255), " +
                    " description TEXT, " +
                    " priority SMALLINT, " +
                    " start_date DATE, " +
                    " due_date DATE, " +
                    " estimated_work_time INT, " +
                    " actual_work_time INT, " +
                    " status SMALLINT);");

            // cascade makes sure that when a task or user is deleted all corresponding rows in that
            // table are deleted too
            queryWithoutResults("CREATE TABLE IF NOT EXISTS \"Task_Assignee\" (" +
                    " task_id INT, " +
                    " user_id INT, " +
                    " PRIMARY KEY (task_id, user_id), " +
                    " FOREIGN KEY (task_id) REFERENCES \"" + tableName + "\"(id) ON DELETE CASCADE, " +
                    " FOREIGN KEY (user_id) REFERENCES \"User\"(id) ON DELETE CASCADE);");
            HiveLog.debug(getClass(), "Created Task table if not exists");
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Failed to create Task table!");
            throw new RuntimeException(e);
        }
    }

    /**
     * Creates a new task in the database and returns the generated task ID.
     * @param userStoryId The ID of the user story this task belongs to
     * @param name The name of the task
     * @param description The description of the task
     * @param priority The priority level of the task
     * @param status The current status of the task
     * @return The newly created Task object with its ID
     * @throws SQLException If there's an error during database operations
     */
    public Task createTask(int userStoryId, String name, String description, PRIORITY priority, TASKSTATUS status) throws SQLException {
        String sql = "INSERT INTO \"" + tableName + "\" (user_story_id, name, description, priority, status) VALUES (?, ?, ?, ?, ?) RETURNING id;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, userStoryId);
            stmt.setString(2, name);
            stmt.setString(3, description);
            stmt.setInt(4, priority.ordinal());
            stmt.setInt(5, status.ordinal());

            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                int taskId = rs.getInt("id");
                Task task = new Task(taskId);
                task.setName(name);
                task.setDescription(description);
                task.setPriority(priority);
                task.setStatus(status);
                return task;
            }
            throw new SQLException("Creating task failed, no ID obtained.");
        }
    }

    /**
     * Creates a new task with additional date and time information.
     * @param userStoryId The ID of the user story this task belongs to
     * @param name The name of the task
     * @param description The description of the task
     * @param priority The priority level of the task
     * @param status The current status of the task
     * @param startDate The start date for the task
     * @param dueDate The due date for the task
     * @param estimatedWorkTime The estimated work time in hours
     * @return The newly created Task object with its ID
     * @throws SQLException If there's an error during database operations
     */
    public Task createTask(int userStoryId, String name, String description, PRIORITY priority, TASKSTATUS status,
                          Date startDate, Date dueDate, int estimatedWorkTime) throws SQLException {
        String sql = "INSERT INTO \"" + tableName + "\" (user_story_id, name, description, priority, status, start_date, due_date, estimated_work_time) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING id;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, userStoryId);
            stmt.setString(2, name);
            stmt.setString(3, description);
            stmt.setInt(4, priority.ordinal());
            stmt.setInt(5, status.ordinal());
            stmt.setDate(6, startDate);
            stmt.setDate(7, dueDate);
            stmt.setInt(8, estimatedWorkTime);

            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                int taskId = rs.getInt("id");
                Task task = new Task(taskId);
                task.setName(name);
                task.setDescription(description);
                task.setPriority(priority);
                task.setStatus(status);
                task.setStarDate(startDate);
                task.setDueDate(dueDate);
                task.setEstimatedWorkTime(estimatedWorkTime);
                return task;
            }
            throw new SQLException("Creating task failed, no ID obtained.");
        }
    }

    /**
     * Retrieves a task by its ID.
     * @param taskId The ID of the task to retrieve
     * @return The Task object or null if not found
     * @throws SQLException If there's an error during database operations
     */
    public Task getTask(int taskId) throws SQLException {
        String sql = "SELECT * FROM \"" + tableName + "\" WHERE id = ?;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, taskId);
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                Task task = new Task(rs.getInt("id"));
                task.setName(rs.getString("name"));
                task.setDescription(rs.getString("description"));

                int priorityOrdinal = rs.getInt("priority");
                task.setPriority(PRIORITY.values()[priorityOrdinal]);

                int statusOrdinal = rs.getInt("status");
                task.setStatus(TASKSTATUS.values()[statusOrdinal]);

                Date startDate = rs.getDate("start_date");
                if (startDate != null) {
                    task.setStarDate(startDate);
                }

                Date dueDate = rs.getDate("due_date");
                if (dueDate != null) {
                    task.setDueDate(dueDate);
                }

                task.setEstimatedWorkTime(rs.getInt("estimated_work_time"));
                task.setActualWorkTime(rs.getInt("actual_work_time"));

                // Load assigned users
                task.getAssignedTo().addAll(getTaskAssignees(taskId));

                return task;
            }
            return null;
        }
    }

    /**
     * Retrieves all tasks for a specific user story.
     * @param userStoryId The ID of the user story
     * @return A list of Task objects
     * @throws SQLException If there's an error during database operations
     */
    public List<Task> getTasksForUserStory(int userStoryId) throws SQLException {
        List<Task> tasks = new ArrayList<>();
        String sql = "SELECT id FROM \"" + tableName + "\" WHERE user_story_id = ?;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, userStoryId);
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                int taskId = rs.getInt("id");
                Task task = getTask(taskId);
                if (task != null) {
                    tasks.add(task);
                }
            }
        }
        return tasks;
    }

    /**
     * Updates an existing task in the database.
     * @param task The Task object with updated values
     * @throws SQLException If there's an error during database operations
     */
    public void updateTask(Task task) throws SQLException {
        String sql = "UPDATE \"" + tableName + "\" SET name = ?, description = ?, priority = ?, status = ?, " +
                    "start_date = ?, due_date = ?, estimated_work_time = ?, actual_work_time = ? WHERE id = ?;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, task.getName());
            stmt.setString(2, task.getDescription());
            stmt.setInt(3, task.getPriority().ordinal());
            stmt.setInt(4, task.getStatus().ordinal());

            if (task.getStarDate() != null) {
                stmt.setDate(5, new java.sql.Date(task.getStarDate().getTime()));
            } else {
                stmt.setNull(5, Types.DATE);
            }

            if (task.getDueDate() != null) {
                stmt.setDate(6, new java.sql.Date(task.getDueDate().getTime()));
            } else {
                stmt.setNull(6, Types.DATE);
            }

            stmt.setInt(7, task.getEstimatedWorkTime());
            stmt.setInt(8, task.getActualWorkTime());
            stmt.setInt(9, task.getId());

            stmt.executeUpdate();
        }
    }

    /**
     * Deletes a task from the database.
     * @param taskId The ID of the task to delete
     * @throws SQLException If there's an error during database operations
     */
    public void deleteTask(int taskId) throws SQLException {
        String sql = "DELETE FROM \"" + tableName + "\" WHERE id = ?;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, taskId);
            stmt.executeUpdate();
        }
    }

    /**
     * Updates the assignees for a task. This implementation clears the current list and inserts the new one.
     * @param taskId The ID of the task
     * @param assignees List of user IDs to assign to the task
     * @throws SQLException If there's an error during database operations
     */
    public void updateTaskAssignees(int taskId, List<Integer> assignees) throws SQLException {
        // Delete all existing assignees for this task
        String deleteSQL = "DELETE FROM \"Task_Assignee\" WHERE task_id = ?;";
        try (PreparedStatement stmt = connection.prepareStatement(deleteSQL)) {
            stmt.setInt(1, taskId);
            stmt.executeUpdate();
        }

        // Insert new assignees (if any)
        if (assignees != null && !assignees.isEmpty()) {
            String insertSQL = "INSERT INTO \"Task_Assignee\" (task_id, user_id) VALUES (?, ?);";
            try (PreparedStatement stmt = connection.prepareStatement(insertSQL)) {
                for (Integer userId : assignees) {
                    stmt.setInt(1, taskId);
                    stmt.setInt(2, userId);
                    stmt.addBatch();
                }
                stmt.executeBatch();
            }
        }
    }

    /**
     * Retrieves the list of user IDs assigned to a task.
     * @param taskId The ID of the task
     * @return A list of user IDs
     * @throws SQLException If there's an error during database operations
     */
    public List<Integer> getTaskAssignees(int taskId) throws SQLException {
        List<Integer> assignees = new ArrayList<>();
        String sql = "SELECT user_id FROM \"Task_Assignee\" WHERE task_id = ?;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, taskId);
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                assignees.add(rs.getInt("user_id"));
            }
        }
        return assignees;
    }

    /**
     * Returns a list of all tasks stored in the database
     * @return List of objects with all tasks
     * @throws SQLException If there's an error during database operations
     */
    public static List<Object[]> getAllTasks() throws SQLException {
        List<Object[]> tasks = new ArrayList<>();
        String sql = "SELECT user_id, task_id, due_date, userstory.id AS userstory_id, project_id, start_date, estimated_work_time FROM " +
                "\"Task\" AS task INNER JOIN \"Task_Assignee\" AS task_assignee ON task.id = task_assignee.task_id " +
                "INNER JOIN \"UserStory\" AS userstory ON userstory.id = task.user_story_id;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                Object[] row = new Object[]{
                        rs.getInt("user_id"),
                        rs.getInt("task_id"),
                        rs.getDate("due_date"),
                        rs.getInt("userstory_id"),
                        rs.getInt("project_id"),
                        rs.getDate("start_date"),
                        rs.getInt("estimated_work_time")
                };
                tasks.add(row);
            }
        } catch (SQLException e) {
            HiveLog.error(HiveLog.class, e.getMessage());
            return tasks;
        }
        return tasks;
    }
}
