package de.flowhive.storage;

import de.flowhive.project.attributes.Sprint;
import de.flowhive.project.attributes.UserStory;
import de.flowhive.utils.HiveLog;

import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class SprintDatabaseImpl extends DatabaseAdapter {

    private final UserStoryDatabaseImpl userStoryDatabase;

    public SprintDatabaseImpl(UserStoryDatabaseImpl userStoryDatabase) {
        super("Sprint");
        this.userStoryDatabase = userStoryDatabase;

        // create table if not exists
        try {
            queryWithoutResults("CREATE TABLE IF NOT EXISTS \"" + tableName + "\" (" +
                    " id SERIAL PRIMARY KEY," +
                    " project_id INT," +
                    " name VARCHAR(255)," +
                    " description TEXT," +
                    " start_date DATE, " +
                    " due_date DATE, " +
                    " FOREIGN KEY (project_id) REFERENCES \"Project\"(id) ON DELETE CASCADE " +
                    ");");

            queryWithoutResults("CREATE TABLE IF NOT EXISTS \"UserStory_Sprint\" (" +
                    " user_story_id INT," +
                    " sprint_id INT," +
                    " PRIMARY KEY (user_story_id, sprint_id)," +
                    " FOREIGN KEY (user_story_id) REFERENCES \"UserStory\"(id) ON DELETE CASCADE," +
                    " FOREIGN KEY (sprint_id) REFERENCES \"Sprint\"(id) ON DELETE CASCADE);");
            HiveLog.debug(getClass(), "Created Sprint table if not exists");
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Failed to create Sprint table!");
            throw new RuntimeException(e);
        }
    }

    /**
     * Creates a new sprint in the database and returns the generated sprint ID.
     * @param projectId The ID of the project this sprint belongs to
     * @param name The name of the sprint
     * @param description The description of the sprint
     * @return The newly created Sprint object with its ID
     * @throws SQLException If there's an error during database operations
     */
    public Sprint createSprint(int projectId, String name, String description, Date start, Date due) throws SQLException {
        String sql = "INSERT INTO \"" + tableName + "\" (project_id, name, description, start_date, due_date) VALUES (?, ?, ?, ?, ?) RETURNING id;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, projectId);
            stmt.setString(2, name);
            stmt.setString(3, description);
            stmt.setDate(4, start);
            stmt.setDate(5, due);

            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                int sprintId = rs.getInt("id");
                Sprint sprint = new Sprint(sprintId);
                sprint.setName(name);
                sprint.setDescription(description);

                // Set the dates - convert java.sql.Date to java.util.Date
                if (start != null) {
                    sprint.setStartDate(new java.util.Date(start.getTime()));
                }
                if (due != null) {
                    sprint.setDueDate(new java.util.Date(due.getTime()));
                }

                return sprint;
            }
            throw new SQLException("Creating sprint failed, no ID obtained.");
        }
    }

    /*
     * Create Sprint without dates
     */
    public Sprint createSprint(int projectId, String name, String description) throws SQLException {
        String sql = "INSERT INTO \"" + tableName + "\" (project_id, name, description) VALUES (?, ?, ?) RETURNING id;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, projectId);
            stmt.setString(2, name);
            stmt.setString(3, description);

            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                int sprintId = rs.getInt("id");
                Sprint sprint = new Sprint(sprintId);
                sprint.setName(name);
                sprint.setDescription(description);
                return sprint;
            }
            throw new SQLException("Creating sprint failed, no ID obtained.");
        }
    }

    /**
     * Updates an existing sprint in the database.
     * @param sprint The Sprint object with updated values
     * @throws SQLException If there's an error during database operations
     */
    public void updateSprint(Sprint sprint) throws SQLException {
        String sql = "UPDATE \"" + tableName + "\" SET name = ?, description = ?, start_date = ?, due_date = ? WHERE id = ?;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, sprint.getName());
            stmt.setString(2, sprint.getDescription());

            // Handle dates - convert java.util.Date to java.sql.Date
            if (sprint.getStartDate() != null) {
                stmt.setDate(3, new Date(sprint.getStartDate().getTime()));
            } else {
                stmt.setDate(3, null);
            }

            if (sprint.getDueDate() != null) {
                stmt.setDate(4, new Date(sprint.getDueDate().getTime()));
            } else {
                stmt.setDate(4, null);
            }

            stmt.setInt(5, sprint.getId());

            stmt.executeUpdate();
        }
    }

    /**
     * Deletes a sprint from the database.
     * @param sprintId The ID of the sprint to delete
     * @throws SQLException If there's an error during database operations
     */
    public void deleteSprint(int sprintId) throws SQLException {
        String sql = "DELETE FROM \"" + tableName + "\" WHERE id = ?;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, sprintId);
            stmt.executeUpdate();
        }
    }

    /**
     * Retrieves a sprint by its ID.
     * @param sprintId The ID of the sprint to retrieve
     * @return The Sprint object or null if not found
     * @throws SQLException If there's an error during database operations
     */
    public Sprint getSprint(int sprintId) throws SQLException {
        String sql = "SELECT * FROM \"" + tableName + "\" WHERE id = ?;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, sprintId);
            ResultSet rs = stmt.executeQuery();

            if (rs.next()) {
                Sprint sprint = new Sprint(sprintId);
                sprint.setName(rs.getString("name"));
                sprint.setDescription(rs.getString("description"));

                // Load dates - convert java.sql.Date to java.util.Date
                Date startDate = rs.getDate("start_date");
                if (startDate != null) {
                    sprint.setStartDate(new java.util.Date(startDate.getTime()));
                }

                Date dueDate = rs.getDate("due_date");
                if (dueDate != null) {
                    sprint.setDueDate(new java.util.Date(dueDate.getTime()));
                }
                // Populate user stories for this sprint
                populateUserStories(sprint);

                return sprint;
            }
            return null;
        }
    }

    /**
     * Retrieves all sprints for a specific project.
     * @param projectId The ID of the project
     * @return A list of Sprint objects
     * @throws SQLException If there's an error during database operations
     */
    public List<Sprint> getSprintsForProject(int projectId) throws SQLException {
        String sql = "SELECT * FROM \"" + tableName + "\" WHERE project_id = ?;";
        List<Sprint> sprints = new ArrayList<>();

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, projectId);
            ResultSet rs = stmt.executeQuery();

            while (rs.next()) {
                int sprintId = rs.getInt("id");
                Sprint sprint = new Sprint(sprintId);
                sprint.setName(rs.getString("name"));
                sprint.setDescription(rs.getString("description"));

                // Load dates - convert java.sql.Date to java.util.Date
                Date startDate = rs.getDate("start_date");
                if (startDate != null) {
                    sprint.setStartDate(new java.util.Date(startDate.getTime()));
                }

                Date dueDate = rs.getDate("due_date");
                if (dueDate != null) {
                    sprint.setDueDate(new java.util.Date(dueDate.getTime()));
                }
                // Populate user stories for this sprint
                populateUserStories(sprint);

                sprints.add(sprint);
            }
        }
        return sprints;
    }

    /**
     * Gets the project ID for a sprint.
     * @param sprintId The ID of the sprint
     * @return The project ID or -1 if not found
     * @throws SQLException If there's an error during database operations
     */
    public int getProjectIdForSprint(int sprintId) throws SQLException {
        String sql = "SELECT project_id FROM \"" + tableName + "\" WHERE id = ?;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, sprintId);
            ResultSet rs = stmt.executeQuery();

            if (rs.next()) {
                return rs.getInt("project_id");
            }
            return -1;
        }
    }

    /**
     * Adds a user story to a sprint.
     * @param sprintId The ID of the sprint
     * @param userStoryId The ID of the user story
     * @throws SQLException If there's an error during database operations
     */
    public void addUserStoryToSprint(int sprintId, int userStoryId) throws SQLException {
        String sql = "INSERT INTO \"UserStory_Sprint\" (user_story_id, sprint_id) VALUES (?, ?);";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, userStoryId);
            stmt.setInt(2, sprintId);
            stmt.executeUpdate();
        }
    }

    /**
     * Removes a user story from a sprint.
     * @param sprintId The ID of the sprint
     * @param userStoryId The ID of the user story
     * @throws SQLException If there's an error during database operations
     */
    public void removeUserStoryFromSprint(int sprintId, int userStoryId) throws SQLException {
        String sql = "DELETE FROM \"UserStory_Sprint\" WHERE user_story_id = ? AND sprint_id = ?;";
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, userStoryId);
            stmt.setInt(2, sprintId);
            stmt.executeUpdate();
        }
    }

    /**
     * Gets all user stories for a sprint.
     * @param sprintId The ID of the sprint
     * @return A list of user story IDs
     * @throws SQLException If there's an error during database operations
     */
    public List<Integer> getUserStoryIdsForSprint(int sprintId) throws SQLException {
        String sql = "SELECT user_story_id FROM \"UserStory_Sprint\" WHERE sprint_id = ?;";
        List<Integer> userStoryIds = new ArrayList<>();

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, sprintId);
            ResultSet rs = stmt.executeQuery();

            while (rs.next()) {
                userStoryIds.add(rs.getInt("user_story_id"));
            }
        }
        return userStoryIds;
    }

    /**
     * Populates the user stories for a given sprint.
     * @param sprint The Sprint object to populate with user stories
     * @throws SQLException If there's an error during database operations
     */
    private void populateUserStories(Sprint sprint) throws SQLException {
        List<Integer> userStoryIds = getUserStoryIdsForSprint(sprint.getId());

        for (Integer userStoryId : userStoryIds) {
            UserStory userStory = userStoryDatabase.getUserStory(userStoryId);
            if (userStory != null) {
                sprint.getUserStories().add(userStory);
            }
        }
    }
}
