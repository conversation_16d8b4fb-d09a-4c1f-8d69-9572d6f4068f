package de.flowhive.storage;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.*;
import de.flowhive.FlowHive;
import de.flowhive.user.ROLE;
import de.flowhive.user.User;
import de.flowhive.utils.HiveLog;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;


public class UserDatabaseImpl extends DatabaseAdapter {

    public UserDatabaseImpl() {
        super("User");

        // create table if not exists
        try {
            queryWithoutResults("CREATE TABLE IF NOT EXISTS \"" + tableName + "\" (" +
                    " id SERIAL PRIMARY KEY," +
                    " username VARCHAR(255) NOT NULL," +
                    " pre_name VARCHAR(255)," +
                    " sur_name VARCHAR(255)," +
                    " mail VARCHAR(255) UNIQUE NOT NULL," +
                    " password VARCHAR(255) NOT NULL," +
                    " inbox JSONB);");
            HiveLog.debug(getClass(), "Created User table if not exists");
        } catch (SQLException e) {
            HiveLog.error(getClass(), "Failed to create User table!");
            throw new RuntimeException(e);
        }
    }

    public Integer addUser(String username, String preName, String surName, String mail, String password) {
        // add user into db
        try {
            queryWithoutResults("INSERT INTO \"User\" (username, pre_name, sur_name, mail, password) " +
                    "VALUES ('" + username + "', '" + preName + "', '" + surName + "', '" + mail + "', '" + password + "');");
        } catch (SQLException e) {
            HiveLog.error(getClass(), e.getMessage());
            return -1;
        }
        try (ResultSet rs = queryWithResults("SELECT id FROM \"User\" WHERE mail='" + mail + "';")) {
            if (rs.next()) {
                return rs.getInt("id");
            }
        } catch (SQLException e) {
            HiveLog.error(getClass(), e.getMessage());
            return -1;
        }
        return -1;
    }

    public boolean updateUser(int id, String username, String preName, String surName, String mail) {
        try (ResultSet rs = queryWithResults("SELECT * FROM \"User\" WHERE id='" + id + "';")) {
            if (rs.next()) {
                rs.updateString("username", username);
                rs.updateString("pre_name", preName);
                rs.updateString("sur_name", surName);
                rs.updateString("mail", mail);
                rs.updateRow();
                return true;
            }
        } catch (SQLException e) {
            HiveLog.error(getClass(), e.getMessage());
        }
        return false;
    }

    public User getUser(int id) {
        try (ResultSet rs = queryWithResults("SELECT * FROM \"User\" WHERE id='" + id + "';")) {
            if (rs.next()) {
                User user = new User(id, rs.getString("username"), rs.getString("pre_name"), rs.getString("sur_name"),
                        rs.getString("mail"), "", new HashMap<>(), new ArrayList<>());

                // get projects
                ResultSet projectRs = queryWithResults("SELECT * FROM \"Project_Member\" WHERE user_id='" + id + "';");
                while (projectRs.next()) {
                    user.getProjects().put(projectRs.getInt("project_id"), ROLE.MEMBER);
                }

                ResultSet projectOwner = queryWithResults("SELECT * FROM \"Project\" WHERE product_owner='" + id + "';");
                while (projectOwner.next()) {
                    user.getProjects().put(projectOwner.getInt("id"), ROLE.PRODUCT_OWNER);
                }

                return user;
            }
        } catch (SQLException e) {
            HiveLog.error(getClass(), e.getMessage());
        }
        return null;
    }

    public String getPassword(int id) {
        try (ResultSet rs = queryWithResults("SELECT password FROM \"User\" WHERE id=" + id + ";")) {
            if (rs.next()) {
                return rs.getString("password");
            }
        } catch (SQLException e) {
            HiveLog.error(getClass(), e.getMessage());
        }
        return null;
    }

    public boolean updatePassword(int id, String password) {
        try (ResultSet rs = queryWithResults("SELECT * FROM \"User\" WHERE id=" + id + ";")) {
            if (rs.next()) {
                rs.updateString("password", password);
                rs.updateRow();
                return true;
            }
        } catch (SQLException e) {
            HiveLog.error(getClass(), e.getMessage());
        }
        return false;
    }

    public User authUser(String username, String password) {
        try (ResultSet rs = queryWithResults("SELECT username, id FROM \"User\" WHERE username='" + username + "' AND password='" + password + "';")) {
            if (rs.next()) {
                return new User(rs.getInt("id"), rs.getString("username"));
            }
        } catch (SQLException e) {
            HiveLog.error(getClass(), e.getMessage());
        }
        return null;
    }

    public boolean uniqueUsername(String username) {
        try (ResultSet rs = queryWithResults("SELECT username FROM \"User\" WHERE username='" + username + "';")) {
            if (rs.next()) {
                HiveLog.log(getClass(), "Username already exists!");
                return false;
            }
        } catch (SQLException e) {
            HiveLog.error(getClass(), e.getMessage());
        }
        return true;
    }

    /**
     * Retrieves a user by their username.
     *
     * @param username The username to search for
     * @return The User object if found, null otherwise
     */
    public User getUserByUsername(String username) {
        try (ResultSet rs = queryWithResults("SELECT * FROM \"User\" WHERE username='" + username + "';")) {
            if (rs.next()) {
                int id = rs.getInt("id");
                User user = new User(id, rs.getString("username"), rs.getString("pre_name"), rs.getString("sur_name"),
                        rs.getString("mail"), "", new HashMap<>(), new ArrayList<>());

                // get projects
                ResultSet projectRs = queryWithResults("SELECT * FROM \"Project_Member\" WHERE user_id='" + id + "';");
                while (projectRs.next()) {
                    user.getProjects().put(projectRs.getInt("project_id"), ROLE.MEMBER);
                }

                ResultSet projectOwner = queryWithResults("SELECT * FROM \"Project\" WHERE product_owner='" + id + "';");
                while (projectOwner.next()) {
                    user.getProjects().put(projectOwner.getInt("id"), ROLE.PRODUCT_OWNER);
                }

                return user;
            }
        } catch (SQLException e) {
            HiveLog.error(getClass(), e.getMessage());
        }
        return null;
    }

    public boolean deleteUser(int id) {
        try {
            queryWithoutResults("DELETE FROM \"User\" WHERE id = " + id + ";");
            return true;
        } catch (SQLException e) {
            HiveLog.error(getClass(), e.getMessage());
            return false;
        }
    }

    public JsonNode getInbox(int userId){
        ObjectMapper mapper = new ObjectMapper();
        try (ResultSet rs = queryWithResults("SELECT inbox FROM \"User\" WHERE id='" + userId + "';")) {
            if (rs.next()) {
                try {
                    String inbox = rs.getString("inbox");
                    if (inbox != null) {
                        return mapper.readTree(inbox);
                    } else {
                        return null;
                    }

                } catch (JsonMappingException e){
                    HiveLog.error(getClass(), e.getMessage());
                }
            }
            else {
                return null;
            }
        } catch (SQLException | JsonProcessingException e) {
            HiveLog.error(getClass(), e.getMessage());
        }
        return null;
    }

    public void updateInbox(int userId, String message) {
        try{
            queryWithoutResults("UPDATE \"User\" Set inbox='" + message + "' WHERE id = " + userId + ";");
        } catch (Exception e) {
            HiveLog.error(getClass(), e.getMessage());
        }
    }
}

