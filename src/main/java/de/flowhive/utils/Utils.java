package de.flowhive.utils;


import com.fasterxml.jackson.databind.ObjectMapper;
import de.flowhive.FlowHive;
import de.flowhive.server.response.JWTCookie;
import de.flowhive.user.User;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;

import javax.crypto.SecretKey;

public class Utils {

    public static final SecretKey SECRET_KEY = Keys.secretKeyFor(SignatureAlgorithm.HS256); // Generate a secure key

    public static User validateJWT(String cookie) throws Exception {
        Claims claims = Jwts.parser().verifyWith(Utils.SECRET_KEY).build().parseSignedClaims(cookie).getPayload();
        JWTCookie jwt = (new ObjectMapper()).convertValue(claims, JWTCookie.class);

        return FlowHive.getInstance().getUserManager().getUser(jwt.getUserId());
    }


}
