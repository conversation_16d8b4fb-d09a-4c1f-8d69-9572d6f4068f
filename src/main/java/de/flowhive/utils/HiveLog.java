package de.flowhive.utils;

import de.flowhive.FlowHive;

public class HiveLog {

    public static void log(Class c, String msg) {
        System.out.println("| INFO: " + c.getSimpleName() +  " -> " + msg);
    }

    public static void debug(Class c, String msg) {
        if (FlowHive.DEBUG) {
            System.out.println("| DEBUG: " + c.getSimpleName() +  " -> " + msg);
        }
    }

    public static void error(Class c, String msg) {
        System.err.println("| ERROR: " + c.getSimpleName() +  " -> " + msg);
    }



}
