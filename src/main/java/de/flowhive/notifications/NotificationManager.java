package de.flowhive.notifications;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.node.*;
import de.flowhive.FlowHive;
import de.flowhive.storage.TaskDatabaseImpl;
import de.flowhive.storage.UserDatabaseImpl;
import de.flowhive.user.UserManager;
import de.flowhive.utils.HiveLog;

import java.sql.SQLException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.time.*;
import java.text.SimpleDateFormat;
import java.util.List;

public class NotificationManager {

    private static NotificationManager instance;

    /**
     * Sends a notification to the inbox of a user
     * Notification consists of: user ID, task ID, user story ID, project ID and a notification message
     * @param userId ID of the user the notification should get send to
     * @param taskId ID of the task that is due (tomorrow/80%)
     * @param userStoryId ID of the user story the task corresponds to
     * @param projectId ID of the project the user story corresponds to
     * @param notificationMessage Notification message sent to the user
     */
    public void send(int userId, int taskId, int userStoryId, int projectId, String notificationMessage) {
        UserManager userManager = FlowHive.getInstance().getUserManager();
        ObjectMapper mapper = new ObjectMapper();
        JsonNode inboxJson = userManager.getInbox(userId);

        if (inboxJson != null) {
            if (inboxJson.isArray()) {
                for (JsonNode node : inboxJson) {
                    if (notificationMessage.equals(node.path("message").asText())) {
                        return;
                    }
                }
            }
        }


        ArrayNode array;
        if (inboxJson == null || !inboxJson.isArray()) {
            array = mapper.createArrayNode();
        } else {
            if (inboxJson.isArray()) {
                array = (ArrayNode) inboxJson;
            } else {
                array = mapper.createArrayNode();
                array.add(inboxJson);
            }
        }

        ObjectNode entry = mapper.createObjectNode();
        entry.put("userId", userId);
        entry.put("taskId", taskId);
        entry.put("userStoryId", userStoryId);
        entry.put("projectId", projectId);
        entry.put("message", notificationMessage);
        array.add(entry);

        String newInboxJson = null;
        try {
            newInboxJson = mapper.writeValueAsString(array);
        } catch (JsonProcessingException e) {
            HiveLog.error(HiveLog.class, e.getMessage());
        }
        userManager.updateInbox(userId, newInboxJson);
    }

    /**
     * Checks due dates of all tasks.
     * Sends a notification to assigned user of a task, if that task is due tomorrow or 80% of the estimated working time has elapsed.
     * @throws SQLException If there's an error during database operations
     */
    public static void checkDates() throws SQLException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        List<Object[]> tasklist;
        tasklist = TaskDatabaseImpl.getAllTasks();
        Date dateToday = new Date();
        String today = sdf.format(dateToday);
        Date dateTomorrow = new Date(dateToday.getTime() + 24 * 60 * 60 * 1000);
        String tomorrow = sdf.format(dateTomorrow);
        for (Object[] task : tasklist) {
            Date dueDate = null;
            Date startDate = null;
            try {
                dueDate = sdf.parse(sdf.format(task[2]));
                startDate = sdf.parse(sdf.format(task[5]));
            } catch (ParseException e) {
                HiveLog.error(HiveLog.class, e.getMessage());
            }
            String eightyDate =  sdf.format(calculateEightyPercentDate(startDate, dueDate));
            NotificationManager notificationManager = FlowHive.getInstance().getNotificationManager();
            if (eightyDate.equals(today)) {
                notificationManager.send((int) task[0], (int) task[1], (int) task[3], (int) task[4], "80% of the estimated working time for task " + task[1] + " has elapsed.");
            }
            if (sdf.format(dueDate).equals(tomorrow)) {
                notificationManager.send((int) task[0], (int) task[1], (int) task[3], (int) task[4], "Task " + task[1] + " is due tomorrow!");
            }
        }
    }

    /**
     * Calculates if 80% of estimated working time has elapsed.
     * @param startDate Start date of the task
     * @param dueDate Due date of the task
     * @return Date, at which 80% of the estimated working time will be elapsed
     */
    public static Date calculateEightyPercentDate(Date startDate, Date dueDate) {
        long startMillis = startDate.getTime();
        long dueMillis = dueDate.getTime();
        long durationMillis = dueMillis - startMillis;
        long eightyPercentMillis = (durationMillis * 80) / 100;
        return new Date(startMillis + eightyPercentMillis);
    }


    public static void main(String[] args) {
    }
}