package de.flowhive.notifications;

public abstract class Notification {

    protected int projectId = -1;
    protected int userId = -1;

    public Notification(int projectId, int userId) {
        this.projectId = projectId;
        this.userId = userId;
    }

    public int getProjectId() {
        return projectId;
    }

    public int getUserId() {
        return userId;
    }

    public abstract String getMessage();
}
