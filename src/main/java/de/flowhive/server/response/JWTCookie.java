package de.flowhive.server.response;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

public class <PERSON><PERSON><PERSON><PERSON><PERSON> implements Serializable {

    private int userId;
    private String userName;

    // Annotation is required so jjwt can use the constructor to decode the jwt
    @JsonCreator
    public JWTCookie(@JsonProperty("userId") int userId, @JsonProperty("userName") String userName) {
        this.userId = userId;
        this.userName = userName;
    }

    public int getUserId() {
        return userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}
