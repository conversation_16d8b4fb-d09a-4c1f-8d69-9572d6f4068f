package de.flowhive.server.routes.user;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import de.flowhive.FlowHive;
import de.flowhive.server.IRoute;
import de.flowhive.server.Request;
import de.flowhive.server.RequestType;
import de.flowhive.server.response.APIResponse;
import de.flowhive.server.response.JWTCookie;
import de.flowhive.user.User;
import de.flowhive.utils.HiveLog;
import de.flowhive.utils.Utils;
import io.javalin.http.Context;
import io.javalin.http.Cookie;
import io.javalin.http.SameSite;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;

import java.security.Key;

@Request(type = RequestType.POST)
public class AuthenticationRoute implements IRoute {

    @Override
    public String getPath() {
        return "/user/authenticate";
    }

    @Override
    public void callback(Context ctx) {
        if (ctx.cookie("auth") != null) {
            ctx.json(new APIResponse(false, "You are already authenticated."));
        }

        JsonElement elem = JsonParser.parseString(ctx.body());
        if (!elem.isJsonObject()) {
            ctx.status(400).json(new APIResponse(false, "Bad request"));
            return;
        }

        JsonObject obj = elem.getAsJsonObject();
        if (!obj.has("username") || !obj.has("password"))
            ctx.status(400).json(new APIResponse(false, "Bad request"));


        String username = obj.get("username").getAsString();
        String password = obj.get("password").getAsString();

        User user = FlowHive.getInstance().getUserManager().authenticate(username, password);
        if (user == null) {
            ctx.status(400).json(new APIResponse(false, "Invalid credentials"));
            return;
        }

        ObjectMapper mapper = new ObjectMapper();
        String payload = null;
        try {
             payload = mapper.writeValueAsString(new JWTCookie(user.getId(), user.getUsername()));
        } catch (JsonProcessingException e) {
            HiveLog.error(getClass(), "Failed to map JWT cookie to JSON");
            e.printStackTrace();
        }

        if (payload == null) {
            ctx.status(500).json(new APIResponse(false, "Failed to create cookie"));
            return;
        }

        String jwtToken = Jwts.builder()
                .content(payload)
                .signWith(Utils.SECRET_KEY)
                .compact();

        Cookie authenticationCookie = new Cookie("auth", jwtToken);
        authenticationCookie.setPath("/");
        authenticationCookie.setSameSite(SameSite.NONE);
        authenticationCookie.setHttpOnly(true);
        if (!FlowHive.DEBUG) authenticationCookie.setDomain("flowhive.sigasoft.de");
        authenticationCookie.setSecure(true);
        authenticationCookie.setMaxAge(60*60*24*7);
        ctx.cookie(authenticationCookie);
        ctx.json(new APIResponse(true, "Authentication Successful"));
    }
}
