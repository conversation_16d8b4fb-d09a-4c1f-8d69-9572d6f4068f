package de.flowhive.server.routes.user;

import de.flowhive.FlowHive;
import de.flowhive.project.Project;
import de.flowhive.project.ProjectManager;
import de.flowhive.project.attributes.Sprint;
import de.flowhive.project.attributes.Task;
import de.flowhive.project.attributes.UserStory;
import de.flowhive.server.IRoute;
import de.flowhive.server.Request;
import de.flowhive.server.RequestType;
import de.flowhive.server.response.APIResponse;
import de.flowhive.user.ROLE;
import de.flowhive.user.User;
import de.flowhive.utils.HiveLog;
import de.flowhive.utils.Utils;
import io.javalin.http.Context;

import java.util.*;

import java.util.Map;

@Request(type = RequestType.GET)
public class FetchGeneralRoute implements IRoute {

    @Override
    public String getPath() {
        return "/user/general";
    }

    @Override
    public void callback(Context ctx) {
        // Check if user is authenticated
        String authCookie = ctx.cookie("auth");

        if (authCookie == null || authCookie.isEmpty()) {
            ctx.status(401).json(new APIResponse(false, "Not signed in."));
            return;
        }

        try {
            // Validate JWT and get user
            de.flowhive.user.User user = Utils.validateJWT(authCookie);
            if (user == null) {
                ctx.status(401).json(new APIResponse(false, "Not signed in."));
                return;
            }

            // Get the full user data
            User fullUser = FlowHive.getInstance().getUserManager().getUser(user.getId());
            if (fullUser == null) {
                ctx.status(404).json(new APIResponse(false, "User not found."));
                return;
            }

            // Create a response object with user data and related entities
            Map<String, Object> responseData = new HashMap<>();

            // Add user data (excluding sensitive information like password)
            Map<String, Object> userData = new HashMap<>();
            userData.put("id", fullUser.getId());
            userData.put("username", fullUser.getUsername());
            userData.put("preName", fullUser.getPreName());
            userData.put("surName", fullUser.getSurName());
            userData.put("mail", fullUser.getMail());
            userData.put("inbox", fullUser.getInbox());

            responseData.put("user", userData);

            // Get projects the user is involved in
            List<Map<String, Object>> projectsData = new ArrayList<>();
            ProjectManager projectManager = FlowHive.getInstance().getProjectManager();
            Set<Integer> memberIds = new HashSet<>();

            // Process each project the user is involved in
            Map<Integer, ROLE> userProjects = fullUser.getProjects();
            for (Map.Entry<Integer, ROLE> entry : userProjects.entrySet()) {
                int projectId = entry.getKey();
                ROLE role = entry.getValue();

                Project project = projectManager.getProject(projectId);
                if (project != null) {
                    Map<String, Object> projectData = new HashMap<>();
                    projectData.put("id", project.getId());
                    projectData.put("name", project.getName());
                    projectData.put("standards", project.getStandards());
                    projectData.put("productOwner", project.getProductOwner());
                    projectData.put("members", project.getMembers());
                    projectData.put("role", role.toString());

                    memberIds.addAll(project.getMembers());
                    memberIds.add(project.getProductOwner());

                    // Get user stories for this project
                    List<UserStory> userStories = projectManager.getUserStoriesForProject(projectId);
                    List<Map<String, Object>> userStoriesData = new ArrayList<>();

                    for (UserStory userStory : userStories) {
                        Map<String, Object> userStoryData = new HashMap<>();
                        userStoryData.put("id", userStory.getId());
                        userStoryData.put("name", userStory.getName());
                        userStoryData.put("description", userStory.getDescription());
                        userStoryData.put("priority", userStory.getPriority());

                        // Get tasks for this user story
                        List<Task> tasks = projectManager.getTasksForUserStory(userStory.getId());
                        List<Map<String, Object>> tasksData = new ArrayList<>();

                        for (Task task : tasks) {
                            // Only include tasks assigned to this user or if user is product owner
                            if (role == ROLE.PRODUCT_OWNER || task.getAssignedTo().contains(fullUser.getId())) {
                                Map<String, Object> taskData = new HashMap<>();
                                taskData.put("id", task.getId());
                                taskData.put("name", task.getName());
                                taskData.put("description", task.getDescription());
                                taskData.put("priority", task.getPriority());
                                taskData.put("status", task.getStatus());
                                taskData.put("startDate", task.getStarDate());
                                taskData.put("dueDate", task.getDueDate());
                                taskData.put("estimatedWorkTime", task.getEstimatedWorkTime());
                                taskData.put("actualWorkTime", task.getActualWorkTime());
                                taskData.put("assignedTo", task.getAssignedTo());

                                tasksData.add(taskData);
                            }
                        }

                        userStoryData.put("tasks", tasksData);
                        userStoriesData.add(userStoryData);
                    }

                    // Get sprints for this project
                    List<Sprint> sprints = projectManager.getSprintsForProject(projectId);
                    List<Map<String, Object>> sprintsData = new ArrayList<>();

                    for (Sprint sprint : sprints) {
                        Map<String, Object> sprintData = new HashMap<>();
                        sprintData.put("id", sprint.getId());
                        sprintData.put("name", sprint.getName());
                        sprintData.put("description", sprint.getDescription());
                        sprintData.put("startDate", sprint.getStartDate());
                        sprintData.put("dueDate", sprint.getDueDate());

                        // Get user stories for this sprint
                        List<UserStory> sprintUserStories = projectManager.getUserStoriesForSprint(sprint.getId());
                        List<Integer> sprintUserStoryIds = new ArrayList<>();

                        for (UserStory userStory : sprintUserStories) {
                            sprintUserStoryIds.add(userStory.getId());
                        }

                        sprintData.put("userStoryIds", sprintUserStoryIds);
                        sprintsData.add(sprintData);
                    }

                    projectData.put("userStories", userStoriesData);
                    projectData.put("sprints", sprintsData);

                    projectsData.add(projectData);
                }
            }
            responseData.put("projects", projectsData);

            List<User> members = FlowHive.getInstance().getUserManager().getUsers(memberIds.stream().toList());
            responseData.put("members", members);

            // Return success response with all the data
            ctx.status(200).json(new APIResponse(true, "User data retrieved successfully", responseData));

        } catch (Exception e) {
            HiveLog.error(getClass(), "Error fetching user general data: " + e.getMessage());
            e.printStackTrace();
            ctx.status(500).json(new APIResponse(false, "Error fetching user data: " + e.getMessage()));
        }
    }
}
