package de.flowhive.server.routes.user;

import de.flowhive.FlowHive;
import de.flowhive.server.IRoute;
import de.flowhive.server.Request;
import de.flowhive.server.RequestType;
import de.flowhive.server.response.APIResponse;
import io.javalin.http.Context;
import io.javalin.http.Cookie;
import io.javalin.http.SameSite;

/**
 * Route to handle user logout.
 * Clears the authentication cookie by setting it to empty with short expiration.
 */
@Request(type = RequestType.POST)
public class LogoutRoute implements IRoute {

    @Override
    public String getPath() {
        return "/user/logout";
    }

    @Override
    public void callback(Context ctx) {
        try {
            // Clear the authentication cookie by setting it to empty string with short expiration
            Cookie logoutCookie = new Cookie("auth", "");
            logoutCookie.setPath("/");
            logoutCookie.setSameSite(SameSite.NONE);
            logoutCookie.setHttpOnly(true);
            if (!FlowHive.DEBUG) logoutCookie.setDomain("flowhive.sigasoft.de");
            logoutCookie.setSecure(true);
            logoutCookie.setMaxAge(5);
            ctx.cookie(logoutCookie);
            
            // Return success response
            ctx.status(200).json(new APIResponse(true, "Logged out successfully"));
            
        } catch (Exception e) {
            e.printStackTrace();
            ctx.status(500).json(new APIResponse(false, "Error during logout: " + e.getMessage()));
        }
    }
}
