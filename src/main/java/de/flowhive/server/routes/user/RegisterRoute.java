package de.flowhive.server.routes.user;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import de.flowhive.FlowHive;
import de.flowhive.server.IRoute;
import de.flowhive.server.Request;
import de.flowhive.server.RequestType;
import de.flowhive.server.response.APIResponse;
import de.flowhive.user.User;
import io.javalin.http.Context;
import org.intellij.lang.annotations.Flow;

@Request(type = RequestType.POST)
public class RegisterRoute implements IRoute {

    @Override
    public String getPath() {
        return "/user/register";
    }

    @Override
    public void callback(Context ctx) {
        if (ctx.cookie("auth") != null) {
            ctx.status(200).json(new APIResponse(false, "You are already authenticated."));
        }

        JsonElement elem = JsonParser.parseString(ctx.body());
        if (!elem.isJsonObject()) {
            ctx.status(400).json(new APIResponse(false, "Bad request"));
            return;
        }

        JsonObject obj = elem.getAsJsonObject();
        if (!obj.has("surName") ||
            !obj.has("username") ||
            !obj.has("preName") ||
            !obj.has("password") ||
            !obj.has("email")) {
            ctx.status(400).json(new APIResponse(false, "Bad request"));
            return;
        }
        String username = obj.get("username").getAsString();
        String password = obj.get("password").getAsString();
        String preName = obj.get("preName").getAsString();
        String surName = obj.get("surName").getAsString();
        String email = obj.get("email").getAsString();

        User user = FlowHive.getInstance().getUserManager().register(username, password, preName, surName, email);
        if (user == null) {
            ctx.status(400).json(new APIResponse(false, "Failed to register user."));
            return;
        }
        ctx.status(200).json(new APIResponse(true, "User registered successfully", user));
    }
}
