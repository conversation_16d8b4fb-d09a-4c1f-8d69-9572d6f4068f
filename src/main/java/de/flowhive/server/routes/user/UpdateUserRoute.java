package de.flowhive.server.routes.user;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import de.flowhive.FlowHive;
import de.flowhive.server.IRoute;
import de.flowhive.server.Request;
import de.flowhive.server.RequestType;
import de.flowhive.server.response.APIResponse;
import de.flowhive.user.User;
import de.flowhive.utils.HiveLog;
import de.flowhive.utils.Utils;
import io.javalin.http.Context;

/**
 * Route to update a user's profile information or password.
 * Expects a JSON object with optional fields: username, preName, surName, email.
 * For password changes, requires oldPassword and newPassword fields.
 */
@Request(type = RequestType.POST)
public class UpdateUserRoute implements IRoute {

    @Override
    public String getPath() {
        return "/user/update";
    }

    @Override
    public void callback(Context ctx) {
        // Check if user is authenticated
        String authCookie = ctx.cookie("auth");
        if (authCookie == null || authCookie.isEmpty()) {
            ctx.status(401).json(new APIResponse(false, "Not signed in."));
            return;
        }

        try {
            // Validate JWT and get user
            User user = Utils.validateJWT(authCookie);
            if (user == null) {
                ctx.status(401).json(new APIResponse(false, "Not signed in."));
                return;
            }

            // Parse request body
            JsonElement elem = JsonParser.parseString(ctx.body());
            if (!elem.isJsonObject()) {
                ctx.status(400).json(new APIResponse(false, "Bad request"));
                return;
            }

            JsonObject obj = elem.getAsJsonObject();
            
            // Check if this is a password update request
            if (obj.has("oldPassword") && obj.has("newPassword")) {
                handlePasswordUpdate(ctx, user, obj);
                return;
            }
            
            // Otherwise, handle profile information update
            handleProfileUpdate(ctx, user, obj);
        } catch (Exception e) {
            HiveLog.error(getClass(), "Error updating user: " + e.getMessage());
            e.printStackTrace();
            ctx.status(500).json(new APIResponse(false, "Error updating user: " + e.getMessage()));
        }
    }
    
    /**
     * Handles a password update request.
     * 
     * @param ctx The Javalin context
     * @param user The authenticated user
     * @param obj The JSON request object
     */
    private void handlePasswordUpdate(Context ctx, User user, JsonObject obj) {
        String oldPassword = obj.get("oldPassword").getAsString();
        String newPassword = obj.get("newPassword").getAsString();
        
        // Validate that old password and new password are not the same
        if (oldPassword.equals(newPassword)) {
            ctx.status(400).json(new APIResponse(false, "New password must be different from the old password"));
            return;
        }
        
        // Reset the password
        boolean success = FlowHive.getInstance().getUserManager().resetPassword(user.getId(), oldPassword, newPassword);
        
        if (success) {
            ctx.status(200).json(new APIResponse(true, "Password updated successfully"));
        } else {
            ctx.status(400).json(new APIResponse(false, "Failed to update password. Please check that your old password is correct and the new password meets the requirements."));
        }
    }
    
    /**
     * Handles a profile information update request.
     * 
     * @param ctx The Javalin context
     * @param user The authenticated user
     * @param obj The JSON request object
     */
    private void handleProfileUpdate(Context ctx, User user, JsonObject obj) {
        // Get the full user data
        User fullUser = FlowHive.getInstance().getUserManager().getUser(user.getId());
        if (fullUser == null) {
            ctx.status(404).json(new APIResponse(false, "User not found."));
            return;
        }
        
        // Extract fields from request (if provided)
        String username = obj.has("username") ? obj.get("username").getAsString() : null;
        String preName = obj.has("preName") ? obj.get("preName").getAsString() : null;
        String surName = obj.has("surName") ? obj.get("surName").getAsString() : null;
        String email = obj.has("email") ? obj.get("email").getAsString() : null;
        
        // Check if any fields were provided
        if (username == null && preName == null && surName == null && email == null) {
            ctx.status(400).json(new APIResponse(false, "No fields to update were provided"));
            return;
        }
        
        // Update the user
        User updatedUser = FlowHive.getInstance().getUserManager().updateUser(
            user.getId(), 
            username, 
            preName, 
            surName, 
            email
        );
        
        if (updatedUser != null) {
            // Create a response object with user data (excluding sensitive information)
            JsonObject userData = new JsonObject();
            userData.addProperty("id", updatedUser.getId());
            userData.addProperty("username", updatedUser.getUsername());
            userData.addProperty("preName", updatedUser.getPreName());
            userData.addProperty("surName", updatedUser.getSurName());
            userData.addProperty("email", updatedUser.getMail());
            
            ctx.status(200).json(new APIResponse(true, "User information updated successfully"));
        } else {
            ctx.status(400).json(new APIResponse(false, "Failed to update user information. Please check that all fields meet the requirements."));
        }
    }
}
