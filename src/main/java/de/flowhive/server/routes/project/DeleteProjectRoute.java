package de.flowhive.server.routes.project;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import de.flowhive.FlowHive;
import de.flowhive.project.Project;
import de.flowhive.server.IRoute;
import de.flowhive.server.Request;
import de.flowhive.server.RequestType;
import de.flowhive.server.response.APIResponse;
import de.flowhive.user.User;
import de.flowhive.utils.Utils;
import io.javalin.http.Context;

/**
 * Route to delete a project.
 * Expects a JSON object with projectId.
 * Only the product owner can delete a project.
 */
@Request(type = RequestType.POST)
public class DeleteProjectRoute implements IRoute {

    @Override
    public String getPath() {
        return "/project/delete";
    }

    @Override
    public void callback(Context ctx) {
        // Check if user is authenticated
        if (ctx.cookie("auth") == null) {
            ctx.status(401).json(new APIResponse(false, "Not signed in."));
            return;
        }

        try {
            // Validate JWT and get user
            User user = Utils.validateJWT(ctx.cookie("auth"));
            if (user == null) {
                ctx.status(401).json(new APIResponse(false, "Not signed in."));
                return;
            }

            // Parse request body
            JsonElement elem = JsonParser.parseString(ctx.body());
            if (!elem.isJsonObject()) {
                ctx.status(400).json(new APIResponse(false, "Bad request"));
                return;
            }

            JsonObject obj = elem.getAsJsonObject();
            if (!obj.has("projectId")) {
                ctx.status(400).json(new APIResponse(false, "Missing required field: projectId"));
                return;
            }

            // Get project ID
            int projectId = obj.get("projectId").getAsInt();
            
            // Get project to verify user has access
            Project project = FlowHive.getInstance().getProjectManager().getProject(projectId);
            if (project == null) {
                ctx.status(404).json(new APIResponse(false, "Project not found"));
                return;
            }
            
            // Check if user is the product owner
            if (project.getProductOwner() != user.getId()) {
                ctx.status(403).json(new APIResponse(false, "You don't have permission to delete this project"));
                return;
            }

            // Delete the project
            FlowHive.getInstance().getProjectManager().deleteProject(projectId);
            
            // Return success response
            ctx.status(200).json(new APIResponse(true, "Project deleted successfully"));
        } catch (NumberFormatException e) {
            ctx.status(400).json(new APIResponse(false, "Invalid project ID format"));
        } catch (Exception e) {
            ctx.status(500).json(new APIResponse(false, "Error deleting project: " + e.getMessage()));
        }
    }
}
