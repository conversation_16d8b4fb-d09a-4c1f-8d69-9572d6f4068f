package de.flowhive.server.routes.project.userstory;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import de.flowhive.FlowHive;
import de.flowhive.project.Project;
import de.flowhive.server.IRoute;
import de.flowhive.server.Request;
import de.flowhive.server.RequestType;
import de.flowhive.server.response.APIResponse;
import de.flowhive.user.User;
import de.flowhive.utils.Utils;
import io.javalin.http.Context;

/**
 * Route to delete a user story.
 * Expects a JSON object with userStoryId.
 */
@Request(type = RequestType.POST)
public class DeleteUserStoryRoute implements IRoute {

    @Override
    public String getPath() {
        return "/project/userstory/delete";
    }

    @Override
    public void callback(Context ctx) {
        // Check if user is authenticated
        if (ctx.cookie("auth") == null) {
            ctx.status(401).json(new APIResponse(false, "Not signed in."));
            return;
        }

        try {
            // Validate JWT and get user
            User user = Utils.validateJWT(ctx.cookie("auth"));
            if (user == null) {
                ctx.status(401).json(new APIResponse(false, "Not signed in."));
                return;
            }

            // Parse request body
            JsonElement elem = JsonParser.parseString(ctx.body());
            if (!elem.isJsonObject()) {
                ctx.status(400).json(new APIResponse(false, "Bad request"));
                return;
            }

            JsonObject obj = elem.getAsJsonObject();
            if (!obj.has("userStoryId")) {
                ctx.status(400).json(new APIResponse(false, "Missing required field: userStoryId"));
                return;
            }

            // Get user story ID
            int userStoryId = obj.get("userStoryId").getAsInt();
            
            // Check if the user story exists
            if (FlowHive.getInstance().getProjectManager().getUserStory(userStoryId) == null) {
                ctx.status(404).json(new APIResponse(false, "User story not found"));
                return;
            }
            
            // Get project ID for the user story
            int projectId = FlowHive.getInstance().getProjectManager().getProjectIdForUserStory(userStoryId);
            if (projectId == -1) {
                ctx.status(404).json(new APIResponse(false, "Project not found for this user story"));
                return;
            }
            
            // Check if the project exists
            Project project = FlowHive.getInstance().getProjectManager().getProject(projectId);
            if (project == null) {
                ctx.status(404).json(new APIResponse(false, "Project not found"));
                return;
            }
            
            // Check if user is the product owner (only product owner can delete user stories)
            if (project.getProductOwner() != user.getId()) {
                ctx.status(403).json(new APIResponse(false, "Only the product owner can delete user stories"));
                return;
            }
            
            // Delete the user story
            FlowHive.getInstance().getProjectManager().deleteUserStory(userStoryId);
            
            // Return success response
            ctx.status(200).json(new APIResponse(true, "User story deleted successfully"));
        } catch (NumberFormatException e) {
            ctx.status(400).json(new APIResponse(false, "Invalid user story ID format"));
        } catch (Exception e) {
            e.printStackTrace();
            ctx.status(500).json(new APIResponse(false, "Error deleting user story: " + e.getMessage()));
        }
    }
}
