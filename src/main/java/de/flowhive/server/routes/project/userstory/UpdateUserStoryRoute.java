package de.flowhive.server.routes.project.userstory;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import de.flowhive.FlowHive;
import de.flowhive.project.PRIORITY;
import de.flowhive.project.Project;
import de.flowhive.project.attributes.UserStory;
import de.flowhive.server.IRoute;
import de.flowhive.server.Request;
import de.flowhive.server.RequestType;
import de.flowhive.server.response.APIResponse;
import de.flowhive.user.User;
import de.flowhive.utils.Utils;
import io.javalin.http.Context;

/**
 * Route to update an existing user story.
 * Expects a JSON object with userStoryId and fields to update.
 */
@Request(type = RequestType.POST)
public class UpdateUserStoryRoute implements IRoute {

    @Override
    public String getPath() {
        return "/project/userstory/update";
    }

    @Override
    public void callback(Context ctx) {
        // Check if user is authenticated
        if (ctx.cookie("auth") == null) {
            ctx.status(401).json(new APIResponse(false, "Not signed in."));
            return;
        }

        try {
            // Validate JWT and get user
            User user = Utils.validateJWT(ctx.cookie("auth"));
            if (user == null) {
                ctx.status(401).json(new APIResponse(false, "Not signed in."));
                return;
            }

            // Parse request body
            JsonElement elem = JsonParser.parseString(ctx.body());
            if (!elem.isJsonObject()) {
                ctx.status(400).json(new APIResponse(false, "Bad request"));
                return;
            }

            JsonObject obj = elem.getAsJsonObject();
            if (!obj.has("userStoryId")) {
                ctx.status(400).json(new APIResponse(false, "Missing required field: userStoryId"));
                return;
            }

            // Get user story ID
            int userStoryId = obj.get("userStoryId").getAsInt();
            
            // Get existing user story
            UserStory userStory = FlowHive.getInstance().getProjectManager().getUserStory(userStoryId);
            if (userStory == null) {
                ctx.status(404).json(new APIResponse(false, "User story not found"));
                return;
            }
            
            // Get project ID for the user story
            int projectId = FlowHive.getInstance().getProjectManager().getProjectIdForUserStory(userStoryId);
            if (projectId == -1) {
                ctx.status(404).json(new APIResponse(false, "Project not found for this user story"));
                return;
            }
            
            // Check if the project exists
            Project project = FlowHive.getInstance().getProjectManager().getProject(projectId);
            if (project == null) {
                ctx.status(404).json(new APIResponse(false, "Project not found"));
                return;
            }
            
            // Check if user is a member or owner of the project
            if (project.getProductOwner() != user.getId() && !project.getMembers().contains(user.getId())) {
                ctx.status(403).json(new APIResponse(false, "You don't have permission to update user stories for this project"));
                return;
            }
            
            boolean userStoryUpdated = false;
            
            // Update user story name if provided
            if (obj.has("name")) {
                userStory.setName(obj.get("name").getAsString());
                userStoryUpdated = true;
            }
            
            // Update user story description if provided
            if (obj.has("description")) {
                userStory.setDescription(obj.get("description").getAsString());
                userStoryUpdated = true;
            }
            
            // Update user story priority if provided
            if (obj.has("priority")) {
                try {
                    PRIORITY priority = PRIORITY.valueOf(obj.get("priority").getAsString().toUpperCase());
                    userStory.setPriority(priority);
                    userStoryUpdated = true;
                } catch (IllegalArgumentException e) {
                    ctx.status(400).json(new APIResponse(false, "Invalid priority value. Valid values are: HIGH, MEDIUM, LOW"));
                    return;
                }
            }
            
            // Update user story if any fields were changed
            UserStory updatedUserStory = null;
            if (userStoryUpdated) {
                updatedUserStory = FlowHive.getInstance().getProjectManager().updateUserStory(userStoryId, userStory);
                if (updatedUserStory == null) {
                    ctx.status(500).json(new APIResponse(false, "Failed to update user story"));
                    return;
                }
            } else {
                updatedUserStory = userStory;
            }
            
            // Return success response
            String message = userStoryUpdated ? "User story updated successfully" : "No changes were made to the user story";
            ctx.status(200).json(new APIResponse(true, message, updatedUserStory));
        } catch (NumberFormatException e) {
            ctx.status(400).json(new APIResponse(false, "Invalid numeric value format"));
        } catch (Exception e) {
            e.printStackTrace();
            ctx.status(500).json(new APIResponse(false, "Error updating user story: " + e.getMessage()));
        }
    }
}
