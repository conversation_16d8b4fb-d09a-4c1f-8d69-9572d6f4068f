package de.flowhive.server.routes.project.userstory;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import de.flowhive.FlowHive;
import de.flowhive.project.PRIORITY;
import de.flowhive.project.Project;
import de.flowhive.project.attributes.UserStory;
import de.flowhive.server.IRoute;
import de.flowhive.server.Request;
import de.flowhive.server.RequestType;
import de.flowhive.server.response.APIResponse;
import de.flowhive.user.User;
import de.flowhive.utils.Utils;
import io.javalin.http.Context;

/**
 * Route to create a new user story.
 * Expects a JSON object with projectId, name, description, and optional priority.
 */
@Request(type = RequestType.POST)
public class CreateUserStoryRoute implements IRoute {

    @Override
    public String getPath() {
        return "/project/userstory/create";
    }

    @Override
    public void callback(Context ctx) {
        // Check if user is authenticated
        if (ctx.cookie("auth") == null) {
            ctx.status(401).json(new APIResponse(false, "Not signed in."));
            return;
        }

        try {
            // Validate JWT and get user
            User user = Utils.validateJWT(ctx.cookie("auth"));
            if (user == null) {
                ctx.status(401).json(new APIResponse(false, "Not signed in."));
                return;
            }

            // Parse request body
            JsonElement elem = JsonParser.parseString(ctx.body());
            if (!elem.isJsonObject()) {
                ctx.status(400).json(new APIResponse(false, "Bad request"));
                return;
            }

            JsonObject obj = elem.getAsJsonObject();
            if (!obj.has("projectId") || !obj.has("name") || !obj.has("description")) {
                ctx.status(400).json(new APIResponse(false, "Missing required fields: projectId, name, description"));
                return;
            }

            // Get required fields
            int projectId = obj.get("projectId").getAsInt();
            String name = obj.get("name").getAsString();
            String description = obj.get("description").getAsString();
            
            // Get optional fields with default values
            PRIORITY priority = PRIORITY.MEDIUM;
            if (obj.has("priority")) {
                try {
                    priority = PRIORITY.valueOf(obj.get("priority").getAsString().toUpperCase());
                } catch (IllegalArgumentException e) {
                    ctx.status(400).json(new APIResponse(false, "Invalid priority value. Valid values are: HIGH, MEDIUM, LOW"));
                    return;
                }
            }
            
            // Check if the project exists
            Project project = FlowHive.getInstance().getProjectManager().getProject(projectId);
            if (project == null) {
                ctx.status(404).json(new APIResponse(false, "Project not found"));
                return;
            }
            
            // Check if user is a member or owner of the project
            if (project.getProductOwner() != user.getId() && !project.getMembers().contains(user.getId())) {
                ctx.status(403).json(new APIResponse(false, "You don't have permission to create user stories for this project"));
                return;
            }
            
            // Create the user story
            UserStory userStory;
            if (obj.has("priority")) {
                userStory = FlowHive.getInstance().getProjectManager().createUserStory(
                        projectId, name, description, priority);
            } else {
                userStory = FlowHive.getInstance().getProjectManager().createUserStory(
                        projectId, name, description);
            }
            
            if (userStory == null) {
                ctx.status(500).json(new APIResponse(false, "Failed to create user story"));
                return;
            }
            
            // Return success response
            ctx.status(201).json(new APIResponse(true, "User story created successfully", userStory));
        } catch (NumberFormatException e) {
            ctx.status(400).json(new APIResponse(false, "Invalid numeric value format"));
        } catch (Exception e) {
            e.printStackTrace();
            ctx.status(500).json(new APIResponse(false, "Error creating user story: " + e.getMessage()));
        }
    }
}
