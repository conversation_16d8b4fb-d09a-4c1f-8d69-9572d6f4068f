package de.flowhive.server.routes.project.sprint;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import de.flowhive.FlowHive;
import de.flowhive.project.Project;
import de.flowhive.project.attributes.Sprint;
import de.flowhive.server.IRoute;
import de.flowhive.server.Request;
import de.flowhive.server.RequestType;
import de.flowhive.server.response.APIResponse;
import de.flowhive.user.User;
import de.flowhive.utils.Utils;
import io.javalin.http.Context;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Route to create a new sprint.
 * Expects a JSON object with projectId, name, and description.
 * Optionally accepts startDate and dueDate in ISO format (yyyy-MM-dd).
 */
@Request(type = RequestType.POST)
public class CreateSprintRoute implements IRoute {

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    public String getPath() {
        return "/project/sprint/create";
    }

    @Override
    public void callback(Context ctx) {
        // Check if user is authenticated
        String authCookie = ctx.cookie("auth");
        if (authCookie == null || authCookie.isEmpty()) {
            ctx.status(401).json(new APIResponse(false, "Not signed in."));
            return;
        }

        try {
            // Validate JWT and get user
            User user = Utils.validateJWT(authCookie);
            if (user == null) {
                ctx.status(401).json(new APIResponse(false, "Not signed in."));
                return;
            }

            // Parse request body
            JsonElement elem = JsonParser.parseString(ctx.body());
            if (!elem.isJsonObject()) {
                ctx.status(400).json(new APIResponse(false, "Bad request"));
                return;
            }

            JsonObject obj = elem.getAsJsonObject();
            if (!obj.has("projectId") || !obj.has("name") || !obj.has("description")) {
                ctx.status(400).json(new APIResponse(false, "Missing required fields: projectId, name, description"));
                return;
            }

            // Get request parameters
            int projectId = obj.get("projectId").getAsInt();
            String name = obj.get("name").getAsString();
            String description = obj.get("description").getAsString();

            // Check if the project exists
            Project project = FlowHive.getInstance().getProjectManager().getProject(projectId);
            if (project == null) {
                ctx.status(404).json(new APIResponse(false, "Project not found"));
                return;
            }

            // Check if the user has permission to create a sprint for this project
            // (either product owner or team member)
            if (project.getProductOwner() != user.getId() && !project.getMembers().contains(user.getId())) {
                ctx.status(403).json(new APIResponse(false, "You don't have permission to create sprints for this project"));
                return;
            }

            // Parse optional dates
            Date startDate = null;
            Date dueDate = null;

            if (obj.has("startDate") && !obj.get("startDate").isJsonNull()) {
                try {
                    startDate = DATE_FORMAT.parse(obj.get("startDate").getAsString());
                } catch (ParseException e) {
                    ctx.status(400).json(new APIResponse(false, "Invalid startDate format. Expected yyyy-MM-dd"));
                    return;
                }
            }

            if (obj.has("dueDate") && !obj.get("dueDate").isJsonNull()) {
                try {
                    dueDate = DATE_FORMAT.parse(obj.get("dueDate").getAsString());
                } catch (ParseException e) {
                    ctx.status(400).json(new APIResponse(false, "Invalid dueDate format. Expected yyyy-MM-dd"));
                    return;
                }
            }

            // Create the sprint with or without dates
            Sprint sprint;
            if (startDate != null && dueDate != null) {
                sprint = FlowHive.getInstance().getProjectManager().createSprint(projectId, name, description, startDate, dueDate);
            } else {
                sprint = FlowHive.getInstance().getProjectManager().createSprint(projectId, name, description);
            }
            if (sprint == null) {
                ctx.status(500).json(new APIResponse(false, "Failed to create sprint"));
                return;
            }

            // Return success response
            ctx.status(201).json(new APIResponse(true, "Sprint created successfully", sprint));
        } catch (NumberFormatException e) {
            ctx.status(400).json(new APIResponse(false, "Invalid numeric value format"));
        } catch (Exception e) {
            e.printStackTrace();
            ctx.status(500).json(new APIResponse(false, "Error creating sprint: " + e.getMessage()));
        }
    }
}
