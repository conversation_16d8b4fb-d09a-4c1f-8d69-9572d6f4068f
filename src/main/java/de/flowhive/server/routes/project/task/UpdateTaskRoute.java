package de.flowhive.server.routes.project.task;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import de.flowhive.FlowHive;
import de.flowhive.project.PRIORITY;
import de.flowhive.project.TASKSTATUS;
import de.flowhive.project.attributes.Task;
import de.flowhive.server.IRoute;
import de.flowhive.server.Request;
import de.flowhive.server.RequestType;
import de.flowhive.server.response.APIResponse;
import de.flowhive.user.User;
import de.flowhive.utils.Utils;
import io.javalin.http.Context;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Route to update an existing task.
 * Expects a JSON object with taskId and fields to update.
 */
@Request(type = RequestType.POST)
public class UpdateTaskRoute implements IRoute {

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    public String getPath() {
        return "/project/task/update";
    }

    @Override
    public void callback(Context ctx) {
        // Check if user is authenticated
        if (ctx.cookie("auth") == null) {
            ctx.status(401).json(new APIResponse(false, "Not signed in."));
            return;
        }

        try {
            // Validate JWT and get user
            User user = Utils.validateJWT(ctx.cookie("auth"));
            if (user == null) {
                ctx.status(401).json(new APIResponse(false, "Not signed in."));
                return;
            }

            // Parse request body
            JsonElement elem = JsonParser.parseString(ctx.body());
            if (!elem.isJsonObject()) {
                ctx.status(400).json(new APIResponse(false, "Bad request"));
                return;
            }

            JsonObject obj = elem.getAsJsonObject();
            if (!obj.has("taskId")) {
                ctx.status(400).json(new APIResponse(false, "Missing required field: taskId"));
                return;
            }

            // Get task ID
            int taskId = obj.get("taskId").getAsInt();
            
            // Get existing task
            Task task = FlowHive.getInstance().getProjectManager().getTask(taskId);
            if (task == null) {
                ctx.status(404).json(new APIResponse(false, "Task not found"));
                return;
            }
            
            // Check if the user has permission to update this task
            // This would require getting the project ID from the task, then checking if the user is a member or owner
            // For now, we'll assume the task ID is valid and the user has permission
            
            boolean taskUpdated = false;
            boolean assigneesUpdated = false;
            
            // Update task name if provided
            if (obj.has("name")) {
                task.setName(obj.get("name").getAsString());
                taskUpdated = true;
            }
            
            // Update task description if provided
            if (obj.has("description")) {
                task.setDescription(obj.get("description").getAsString());
                taskUpdated = true;
            }
            
            // Update task priority if provided
            if (obj.has("priority")) {
                try {
                    PRIORITY priority = PRIORITY.valueOf(obj.get("priority").getAsString().toUpperCase());
                    task.setPriority(priority);
                    taskUpdated = true;
                } catch (IllegalArgumentException e) {
                    ctx.status(400).json(new APIResponse(false, "Invalid priority value. Valid values are: HIGH, MEDIUM, LOW"));
                    return;
                }
            }
            
            // Update task status if provided
            if (obj.has("status")) {
                try {
                    TASKSTATUS status = TASKSTATUS.valueOf(obj.get("status").getAsString().toUpperCase());
                    task.setStatus(status);
                    taskUpdated = true;
                } catch (IllegalArgumentException e) {
                    ctx.status(400).json(new APIResponse(false, "Invalid status value. Valid values are: NOT_STARTED, IN_PROGRESS, UNDER_REVIEW, TESTING, DONE"));
                    return;
                }
            }
            
            // Update start date if provided
            if (obj.has("startDate")) {
                try {
                    Date startDate = DATE_FORMAT.parse(obj.get("startDate").getAsString());
                    task.setStarDate(startDate);
                    taskUpdated = true;
                } catch (ParseException e) {
                    ctx.status(400).json(new APIResponse(false, "Invalid start date format. Use yyyy-MM-dd"));
                    return;
                }
            }
            
            // Update due date if provided
            if (obj.has("dueDate")) {
                try {
                    Date dueDate = DATE_FORMAT.parse(obj.get("dueDate").getAsString());
                    task.setDueDate(dueDate);
                    taskUpdated = true;
                } catch (ParseException e) {
                    ctx.status(400).json(new APIResponse(false, "Invalid due date format. Use yyyy-MM-dd"));
                    return;
                }
            }
            
            // Update estimated work time if provided
            if (obj.has("estimatedWorkTime")) {
                task.setEstimatedWorkTime(obj.get("estimatedWorkTime").getAsInt());
                taskUpdated = true;
            }
            
            // Update actual work time if provided
            if (obj.has("actualWorkTime")) {
                task.setActualWorkTime(obj.get("actualWorkTime").getAsInt());
                taskUpdated = true;
            }
            
            // Update task if any fields were changed
            Task updatedTask = null;
            if (taskUpdated) {
                updatedTask = FlowHive.getInstance().getProjectManager().updateTask(taskId, task);
                if (updatedTask == null) {
                    ctx.status(500).json(new APIResponse(false, "Failed to update task"));
                    return;
                }
            }
            
            // Update assignees if provided
            if (obj.has("assignees")) {
                List<Integer> assignees = new ArrayList<>();
                JsonArray assigneesArray = obj.get("assignees").getAsJsonArray();
                for (JsonElement assigneeElem : assigneesArray) {
                    assignees.add(assigneeElem.getAsInt());
                }
                
                boolean success = FlowHive.getInstance().getProjectManager().updateTaskAssignees(taskId, assignees);
                if (!success) {
                    ctx.status(500).json(new APIResponse(false, "Failed to update task assignees"));
                    return;
                }
                assigneesUpdated = true;
            }
            
            // Get updated task
            if (updatedTask == null || assigneesUpdated) {
                updatedTask = FlowHive.getInstance().getProjectManager().getTask(taskId);
            }
            
            // Return success response
            String message = "Task updated successfully";
            if (taskUpdated && assigneesUpdated) {
                message = "Task details and assignees updated successfully";
            } else if (assigneesUpdated) {
                message = "Task assignees updated successfully";
            } else if (taskUpdated) {
                message = "Task details updated successfully";
            } else {
                message = "No changes were made to the task";
            }
            
            ctx.status(200).json(new APIResponse(true, message, updatedTask));
        } catch (NumberFormatException e) {
            ctx.status(400).json(new APIResponse(false, "Invalid numeric value format"));
        } catch (Exception e) {
            e.printStackTrace();
            ctx.status(500).json(new APIResponse(false, "Error updating task: " + e.getMessage()));
        }
    }
}
