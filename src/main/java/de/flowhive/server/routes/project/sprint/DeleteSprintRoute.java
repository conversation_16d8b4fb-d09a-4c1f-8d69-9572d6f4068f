package de.flowhive.server.routes.project.sprint;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import de.flowhive.FlowHive;
import de.flowhive.project.Project;
import de.flowhive.server.IRoute;
import de.flowhive.server.Request;
import de.flowhive.server.RequestType;
import de.flowhive.server.response.APIResponse;
import de.flowhive.user.User;
import de.flowhive.utils.Utils;
import io.javalin.http.Context;

/**
 * Route to delete a sprint.
 * Expects a JSON object with sprintId.
 */
@Request(type = RequestType.POST)
public class DeleteSprintRoute implements IRoute {

    @Override
    public String getPath() {
        return "/project/sprint/delete";
    }

    @Override
    public void callback(Context ctx) {
        // Check if user is authenticated
        String authCookie = ctx.cookie("auth");
        if (authCookie == null || authCookie.isEmpty()) {
            ctx.status(401).json(new APIResponse(false, "Not signed in."));
            return;
        }

        try {
            // Validate JWT and get user
            User user = Utils.validateJWT(authCookie);
            if (user == null) {
                ctx.status(401).json(new APIResponse(false, "Not signed in."));
                return;
            }

            // Parse request body
            JsonElement elem = JsonParser.parseString(ctx.body());
            if (!elem.isJsonObject()) {
                ctx.status(400).json(new APIResponse(false, "Bad request"));
                return;
            }

            JsonObject obj = elem.getAsJsonObject();
            if (!obj.has("sprintId")) {
                ctx.status(400).json(new APIResponse(false, "Missing required field: sprintId"));
                return;
            }

            // Get sprint ID
            int sprintId = obj.get("sprintId").getAsInt();
            
            // Check if the sprint exists
            if (FlowHive.getInstance().getProjectManager().getSprint(sprintId) == null) {
                ctx.status(404).json(new APIResponse(false, "Sprint not found"));
                return;
            }
            
            // Get project ID for this sprint
            int projectId = FlowHive.getInstance().getProjectManager().getProjectIdForSprint(sprintId);
            if (projectId == -1) {
                ctx.status(404).json(new APIResponse(false, "Project not found for this sprint"));
                return;
            }
            
            // Check if the user has permission to delete this sprint
            Project project = FlowHive.getInstance().getProjectManager().getProject(projectId);
            if (project == null) {
                ctx.status(404).json(new APIResponse(false, "Project not found"));
                return;
            }
            
            if (project.getProductOwner() != user.getId() && !project.getMembers().contains(user.getId())) {
                ctx.status(403).json(new APIResponse(false, "You don't have permission to delete sprints for this project"));
                return;
            }
            
            // Delete the sprint
            FlowHive.getInstance().getProjectManager().deleteSprint(sprintId);
            
            // Return success response
            ctx.status(200).json(new APIResponse(true, "Sprint deleted successfully"));
        } catch (NumberFormatException e) {
            ctx.status(400).json(new APIResponse(false, "Invalid sprint ID format"));
        } catch (Exception e) {
            e.printStackTrace();
            ctx.status(500).json(new APIResponse(false, "Error deleting sprint: " + e.getMessage()));
        }
    }
}
