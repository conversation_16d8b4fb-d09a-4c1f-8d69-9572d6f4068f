package de.flowhive.server.routes.project;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import de.flowhive.FlowHive;
import de.flowhive.project.Project;
import de.flowhive.server.IRoute;
import de.flowhive.server.Request;
import de.flowhive.server.RequestType;
import de.flowhive.server.response.APIResponse;
import de.flowhive.user.User;
import de.flowhive.utils.Utils;
import io.javalin.http.Context;

import java.util.ArrayList;
import java.util.List;

/**
 * Route to update a project's details including name, standards, and team members.
 * Expects a JSON object with projectId and optional fields: name, standards, removeMembers, inviteMembers.
 * Only the product owner can update a project.
 *
 * removeMembers: Array of user IDs to remove from the project
 * inviteMembers: Array of usernames to invite to the project
 */
@Request(type = RequestType.POST)
public class UpdateProjectRoute implements IRoute {

    @Override
    public String getPath() {
        return "/project/update";
    }

    @Override
    public void callback(Context ctx) {
        // Check if user is authenticated
        if (ctx.cookie("auth") == null) {
            ctx.status(401).json(new APIResponse(false, "Not signed in."));
            return;
        }

        try {
            // Validate JWT and get user
            User user = Utils.validateJWT(ctx.cookie("auth"));
            if (user == null) {
                ctx.status(401).json(new APIResponse(false, "Not signed in."));
                return;
            }

            // Parse request body
            JsonElement elem = JsonParser.parseString(ctx.body());
            if (!elem.isJsonObject()) {
                ctx.status(400).json(new APIResponse(false, "Bad request"));
                return;
            }

            JsonObject obj = elem.getAsJsonObject();
            if (!obj.has("projectId")) {
                ctx.status(400).json(new APIResponse(false, "Missing required field: projectId"));
                return;
            }

            // Get project ID
            int projectId = obj.get("projectId").getAsInt();

            // Get project to verify user has access
            Project project = FlowHive.getInstance().getProjectManager().getProject(projectId);
            if (project == null) {
                ctx.status(404).json(new APIResponse(false, "Project not found"));
                return;
            }

            // Check if user is the product owner
            if (project.getProductOwner() != user.getId()) {
                ctx.status(403).json(new APIResponse(false, "You don't have permission to modify this project"));
                return;
            }

            boolean projectUpdated = false;
            boolean membersUpdated = false;

            // Update project name if provided
            if (obj.has("name")) {
                String name = obj.get("name").getAsString();
                project.setName(name);
                projectUpdated = true;
            }

            // Update project standards if provided
            if (obj.has("standards")) {
                String standards = obj.get("standards").getAsString();
                project.setStandards(standards);
                projectUpdated = true;
            }

            // Update project if name or standards were changed
            if (projectUpdated) {
                FlowHive.getInstance().getProjectManager().updateProject(project);
            }

            // Handle member management
            boolean membersChanged = false;
            List<Integer> currentMembers = new ArrayList<>(project.getMembers());

            // Remove members if provided
            if (obj.has("removeMembers")) {
                JsonArray removeMembersArray = obj.get("removeMembers").getAsJsonArray();
                for (JsonElement memberElem : removeMembersArray) {
                    int memberId = memberElem.getAsInt();
                    currentMembers.remove(Integer.valueOf(memberId)); // Use valueOf to remove the object, not the index
                    membersChanged = true;
                }
            }

            // Add new members if provided
            if (obj.has("inviteMembers")) {
                JsonArray inviteMembersArray = obj.get("inviteMembers").getAsJsonArray();
                for (JsonElement memberElem : inviteMembersArray) {
                    String username = memberElem.getAsString();
                    User memberToAdd = FlowHive.getInstance().getUserManager().getUserByUsername(username);
                    if (memberToAdd != null && !currentMembers.contains(memberToAdd.getId())) {
                        currentMembers.add(memberToAdd.getId());
                        membersChanged = true;
                    }
                }
            }

            // Update members if changes were made
            if (membersChanged) {
                FlowHive.getInstance().getProjectManager().manageMembers(projectId, currentMembers);
                membersUpdated = true;
            }


            // Get updated project
            Project updatedProject = FlowHive.getInstance().getProjectManager().getProject(projectId);

            // Return success response
            String message = "Project updated successfully";
            if (projectUpdated && membersUpdated) {
                message = "Project details and team members updated successfully";
            } else if (membersUpdated) {
                message = "Team members updated successfully";
            } else if (projectUpdated) {
                message = "Project details updated successfully";
            } else {
                message = "No changes were made to the project";
            }

            ctx.status(200).json(new APIResponse(true, message, updatedProject));
        } catch (NumberFormatException e) {
            ctx.status(400).json(new APIResponse(false, "Invalid project ID or member ID format"));
        } catch (Exception e) {
            ctx.status(500).json(new APIResponse(false, "Error updating project: " + e.getMessage()));
        }
    }
}
