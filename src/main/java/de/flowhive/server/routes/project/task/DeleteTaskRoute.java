package de.flowhive.server.routes.project.task;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import de.flowhive.FlowHive;
import de.flowhive.server.IRoute;
import de.flowhive.server.Request;
import de.flowhive.server.RequestType;
import de.flowhive.server.response.APIResponse;
import de.flowhive.user.User;
import de.flowhive.utils.Utils;
import io.javalin.http.Context;

/**
 * Route to delete a task.
 * Expects a JSON object with taskId.
 */
@Request(type = RequestType.POST)
public class DeleteTaskRoute implements IRoute {

    @Override
    public String getPath() {
        return "/project/task/delete";
    }

    @Override
    public void callback(Context ctx) {
        // Check if user is authenticated
        if (ctx.cookie("auth") == null) {
            ctx.status(401).json(new APIResponse(false, "Not signed in."));
            return;
        }

        try {
            // Validate JWT and get user
            User user = Utils.validateJWT(ctx.cookie("auth"));
            if (user == null) {
                ctx.status(401).json(new APIResponse(false, "Not signed in."));
                return;
            }

            // Parse request body
            JsonElement elem = JsonParser.parseString(ctx.body());
            if (!elem.isJsonObject()) {
                ctx.status(400).json(new APIResponse(false, "Bad request"));
                return;
            }

            JsonObject obj = elem.getAsJsonObject();
            if (!obj.has("taskId")) {
                ctx.status(400).json(new APIResponse(false, "Missing required field: taskId"));
                return;
            }

            // Get task ID
            int taskId = obj.get("taskId").getAsInt();
            
            // Check if the task exists
            if (FlowHive.getInstance().getProjectManager().getTask(taskId) == null) {
                ctx.status(404).json(new APIResponse(false, "Task not found"));
                return;
            }
            
            // Check if the user has permission to delete this task
            // This would require getting the project ID from the task, then checking if the user is a member or owner
            // For now, we'll assume the task ID is valid and the user has permission
            
            // Delete the task
            FlowHive.getInstance().getProjectManager().deleteTask(taskId);
            
            // Return success response
            ctx.status(200).json(new APIResponse(true, "Task deleted successfully"));
        } catch (NumberFormatException e) {
            ctx.status(400).json(new APIResponse(false, "Invalid task ID format"));
        } catch (Exception e) {
            e.printStackTrace();
            ctx.status(500).json(new APIResponse(false, "Error deleting task: " + e.getMessage()));
        }
    }
}
