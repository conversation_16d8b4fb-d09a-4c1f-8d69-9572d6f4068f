package de.flowhive.server.routes.project;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import de.flowhive.FlowHive;
import de.flowhive.project.Project;
import de.flowhive.server.IRoute;
import de.flowhive.server.Request;
import de.flowhive.server.RequestType;
import de.flowhive.server.response.APIResponse;
import de.flowhive.server.response.JWTCookie;
import de.flowhive.user.User;
import de.flowhive.utils.Utils;
import io.javalin.http.Context;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;

import java.security.Key;

@Request(type = RequestType.POST)
public class CreateProjectRoute implements IRoute {


    @Override
    public String getPath() {
        return "/project/create";
    }

    @Override
    public void callback(Context ctx) {
        String authCookie = ctx.cookie("auth");
        if (authCookie == null || authCookie.isEmpty()) {
            ctx.status(401).json(new APIResponse(false, "Not signed in."));
            return;
        }

        try {
            User user = Utils.validateJWT(authCookie);
            if (user == null) {
                ctx.status(401).json(new APIResponse(false, "Not signed in."));
                return;
            }

            JsonElement elem = JsonParser.parseString(ctx.body());
            if (!elem.isJsonObject()) {
                ctx.status(400).json(new APIResponse(false, "Bad request"));
                return;
            }

            JsonObject obj = elem.getAsJsonObject();
            if (!obj.has("name")) {
                ctx.status(400).json(new APIResponse(false, "Bad request: name is required"));
                return;
            }
            String name = obj.get("name").getAsString();

            // Check if standards are provided
            String standards = null;
            if (obj.has("standards")) {
                standards = obj.get("standards").getAsString();
            }

            Project p;
            if (standards != null) {
                p = FlowHive.getInstance().getProjectManager().createProject(user.getId(), name, standards);
            } else {
                p = FlowHive.getInstance().getProjectManager().createProject(user.getId(), name);
            }

            if (p == null) {
                ctx.status(500).json(new APIResponse(false, "Failed to create project"));
                return;
            }

            ctx.status(201).json(new APIResponse(true, "Project created successfully", p));
        } catch (Exception e) {
            e.printStackTrace();
            ctx.status(401).json(new APIResponse(false, e.getMessage()));
        }
    }
}
