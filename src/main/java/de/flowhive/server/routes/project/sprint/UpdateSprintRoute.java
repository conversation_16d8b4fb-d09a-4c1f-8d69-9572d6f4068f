package de.flowhive.server.routes.project.sprint;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import de.flowhive.FlowHive;
import de.flowhive.project.Project;
import de.flowhive.project.attributes.Sprint;
import de.flowhive.server.IRoute;
import de.flowhive.server.Request;
import de.flowhive.server.RequestType;
import de.flowhive.server.response.APIResponse;
import de.flowhive.user.User;
import de.flowhive.utils.Utils;
import io.javalin.http.Context;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Route to update an existing sprint.
 * Expects a JSON object with sprintId and fields to update.
 * Optionally can include userStoryIds to add to the sprint and startDate/dueDate in yyyy-MM-dd format.
 */
@Request(type = RequestType.POST)
public class UpdateSprintRoute implements IRoute {

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    public String getPath() {
        return "/project/sprint/update";
    }

    @Override
    public void callback(Context ctx) {
        // Check if user is authenticated
        String authCookie = ctx.cookie("auth");
        if (authCookie == null || authCookie.isEmpty()) {
            ctx.status(401).json(new APIResponse(false, "Not signed in."));
            return;
        }

        try {
            // Validate JWT and get user
            User user = Utils.validateJWT(authCookie);
            if (user == null) {
                ctx.status(401).json(new APIResponse(false, "Not signed in."));
                return;
            }

            // Parse request body
            JsonElement elem = JsonParser.parseString(ctx.body());
            if (!elem.isJsonObject()) {
                ctx.status(400).json(new APIResponse(false, "Bad request"));
                return;
            }

            JsonObject obj = elem.getAsJsonObject();
            if (!obj.has("sprintId")) {
                ctx.status(400).json(new APIResponse(false, "Missing required field: sprintId"));
                return;
            }

            // Get sprint ID
            int sprintId = obj.get("sprintId").getAsInt();

            // Get existing sprint
            Sprint sprint = FlowHive.getInstance().getProjectManager().getSprint(sprintId);
            if (sprint == null) {
                ctx.status(404).json(new APIResponse(false, "Sprint not found"));
                return;
            }

            // Get project ID for this sprint
            int projectId = FlowHive.getInstance().getProjectManager().getProjectIdForSprint(sprintId);
            if (projectId == -1) {
                ctx.status(404).json(new APIResponse(false, "Project not found for this sprint"));
                return;
            }

            // Check if the user has permission to update this sprint
            Project project = FlowHive.getInstance().getProjectManager().getProject(projectId);
            if (project == null) {
                ctx.status(404).json(new APIResponse(false, "Project not found"));
                return;
            }

            if (project.getProductOwner() != user.getId() && !project.getMembers().contains(user.getId())) {
                ctx.status(403).json(new APIResponse(false, "You don't have permission to update sprints for this project"));
                return;
            }

            // Track what was updated
            boolean sprintUpdated = false;
            boolean userStoriesUpdated = false;

            // Update sprint details if provided
            if (obj.has("name")) {
                sprint.setName(obj.get("name").getAsString());
                sprintUpdated = true;
            }

            if (obj.has("description")) {
                sprint.setDescription(obj.get("description").getAsString());
                sprintUpdated = true;
            }

            // Update start date if provided
            if (obj.has("startDate")) {
                if (obj.get("startDate").isJsonNull()) {
                    sprint.setStartDate(null);
                    sprintUpdated = true;
                } else {
                    try {
                        Date startDate = DATE_FORMAT.parse(obj.get("startDate").getAsString());
                        sprint.setStartDate(startDate);
                        sprintUpdated = true;
                    } catch (ParseException e) {
                        ctx.status(400).json(new APIResponse(false, "Invalid startDate format. Expected yyyy-MM-dd"));
                        return;
                    }
                }
            }

            // Update due date if provided
            if (obj.has("dueDate")) {
                if (obj.get("dueDate").isJsonNull()) {
                    sprint.setDueDate(null);
                    sprintUpdated = true;
                } else {
                    try {
                        Date dueDate = DATE_FORMAT.parse(obj.get("dueDate").getAsString());
                        sprint.setDueDate(dueDate);
                        sprintUpdated = true;
                    } catch (ParseException e) {
                        ctx.status(400).json(new APIResponse(false, "Invalid dueDate format. Expected yyyy-MM-dd"));
                        return;
                    }
                }
            }

            // Update the sprint if details were changed
            Sprint updatedSprint = sprint;
            if (sprintUpdated) {
                updatedSprint = FlowHive.getInstance().getProjectManager().updateSprint(sprintId, sprint);
                if (updatedSprint == null) {
                    ctx.status(500).json(new APIResponse(false, "Failed to update sprint details"));
                    return;
                }
            }

            // Handle user story assignments if provided
            if (obj.has("userStoryIds") && obj.get("userStoryIds").isJsonArray()) {
                JsonArray userStoryArray = obj.get("userStoryIds").getAsJsonArray();
                List<Integer> userStoryIds = new ArrayList<>();

                for (JsonElement userStoryElem : userStoryArray) {
                    userStoryIds.add(userStoryElem.getAsInt());
                }

                // For each user story, add it to the sprint
                for (Integer userStoryId : userStoryIds) {
                    boolean added = FlowHive.getInstance().getProjectManager().addUserStoryToSprint(sprintId, userStoryId);
                    if (added) {
                        userStoriesUpdated = true;
                    }
                }
            }

            // Handle user story removals if provided
            if (obj.has("removeUserStoryIds") && obj.get("removeUserStoryIds").isJsonArray()) {
                JsonArray removeUserStoryArray = obj.get("removeUserStoryIds").getAsJsonArray();
                List<Integer> removeUserStoryIds = new ArrayList<>();

                for (JsonElement userStoryElem : removeUserStoryArray) {
                    removeUserStoryIds.add(userStoryElem.getAsInt());
                }

                // For each user story, remove it from the sprint
                for (Integer userStoryId : removeUserStoryIds) {
                    boolean removed = FlowHive.getInstance().getProjectManager().removeUserStoryFromSprint(sprintId, userStoryId);
                    if (removed) {
                        userStoriesUpdated = true;
                    }
                }
            }

            // Return success response
            String message = "Sprint updated successfully";
            if (sprintUpdated && userStoriesUpdated) {
                message = "Sprint details and user stories updated successfully";
            } else if (userStoriesUpdated) {
                message = "Sprint user stories updated successfully";
            } else if (sprintUpdated) {
                message = "Sprint details updated successfully";
            } else {
                message = "No changes were made to the sprint";
            }

            // Fetch the latest sprint object with populated user stories
            updatedSprint = FlowHive.getInstance().getProjectManager().getSprint(sprintId);
            ctx.status(200).json(new APIResponse(true, message, updatedSprint));
        } catch (NumberFormatException e) {
            ctx.status(400).json(new APIResponse(false, "Invalid numeric value format"));
        } catch (Exception e) {
            e.printStackTrace();
            ctx.status(500).json(new APIResponse(false, "Error updating sprint: " + e.getMessage()));
        }
    }
}
