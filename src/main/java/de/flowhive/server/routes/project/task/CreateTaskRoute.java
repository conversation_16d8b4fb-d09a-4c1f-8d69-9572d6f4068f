package de.flowhive.server.routes.project.task;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import de.flowhive.FlowHive;
import de.flowhive.project.PRIORITY;
import de.flowhive.project.TASKSTATUS;
import de.flowhive.project.Project;
import de.flowhive.project.attributes.Task;
import de.flowhive.server.IRoute;
import de.flowhive.server.Request;
import de.flowhive.server.RequestType;
import de.flowhive.server.response.APIResponse;
import de.flowhive.user.User;
import de.flowhive.utils.Utils;
import io.javalin.http.Context;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Route to create a new task.
 * Expects a JSON object with userStoryId, name, description, and optional fields.
 */
@Request(type = RequestType.POST)
public class CreateTaskRoute implements IRoute {

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    public String getPath() {
        return "/project/task/create";
    }

    @Override
    public void callback(Context ctx) {
        // Check if user is authenticated
        // TODO make validation of auth cookie in all request like in CreateProject by checking for empty string
        // and saving it in a variable
        if (ctx.cookie("auth") == null) {
            ctx.status(401).json(new APIResponse(false, "Not signed in."));
            return;
        }

        try {
            // Validate JWT and get user
            User user = Utils.validateJWT(ctx.cookie("auth"));
            if (user == null) {
                ctx.status(401).json(new APIResponse(false, "Not signed in."));
                return;
            }

            // Parse request body
            JsonElement elem = JsonParser.parseString(ctx.body());
            if (!elem.isJsonObject()) {
                ctx.status(400).json(new APIResponse(false, "Bad request"));
                return;
            }

            JsonObject obj = elem.getAsJsonObject();
            if (!obj.has("userStoryId") || !obj.has("name") || !obj.has("description")) {
                ctx.status(400).json(new APIResponse(false, "Missing required fields: userStoryId, name, description"));
                return;
            }

            // Get required fields
            int userStoryId = obj.get("userStoryId").getAsInt();
            String name = obj.get("name").getAsString();
            String description = obj.get("description").getAsString();
            
            // Get optional fields with default values
            PRIORITY priority = PRIORITY.MEDIUM;
            if (obj.has("priority")) {
                try {
                    priority = PRIORITY.valueOf(obj.get("priority").getAsString().toUpperCase());
                } catch (IllegalArgumentException e) {
                    ctx.status(400).json(new APIResponse(false, "Invalid priority value. Valid values are: HIGH, MEDIUM, LOW"));
                    return;
                }
            }
            
            TASKSTATUS status = TASKSTATUS.NOT_STARTED;
            if (obj.has("status")) {
                try {
                    status = TASKSTATUS.valueOf(obj.get("status").getAsString().toUpperCase());
                } catch (IllegalArgumentException e) {
                    ctx.status(400).json(new APIResponse(false, "Invalid status value. Valid values are: NOT_STARTED, IN_PROGRESS, UNDER_REVIEW, TESTING, DONE"));
                    return;
                }
            }
            
            // Check if the user has permission to create a task in this user story
            // This would require getting the project ID from the user story, then checking if the user is a member or owner
            // For now, we'll assume the user story ID is valid and the user has permission
            
            Task task;
            
            // Check if dates and estimated work time are provided
            if (obj.has("startDate") && obj.has("dueDate") && obj.has("estimatedWorkTime")) {
                // Parse dates
                Date startDate;
                Date dueDate;
                try {
                    startDate = DATE_FORMAT.parse(obj.get("startDate").getAsString());
                    dueDate = DATE_FORMAT.parse(obj.get("dueDate").getAsString());
                } catch (ParseException e) {
                    ctx.status(400).json(new APIResponse(false, "Invalid date format. Use yyyy-MM-dd"));
                    return;
                }
                
                int estimatedWorkTime = obj.get("estimatedWorkTime").getAsInt();
                
                // Create task with all details
                task = FlowHive.getInstance().getProjectManager().createTask(
                        userStoryId, name, description, priority, status, startDate, dueDate, estimatedWorkTime);
            } else {
                // Create basic task
                task = FlowHive.getInstance().getProjectManager().createTask(
                        userStoryId, name, description, priority, status);
            }
            
            if (task == null) {
                ctx.status(500).json(new APIResponse(false, "Failed to create task"));
                return;
            }
            
            // Return success response
            ctx.status(201).json(new APIResponse(true, "Task created successfully", task));
        } catch (NumberFormatException e) {
            ctx.status(400).json(new APIResponse(false, "Invalid numeric value format"));
        } catch (Exception e) {
            e.printStackTrace();
            ctx.status(500).json(new APIResponse(false, "Error creating task: " + e.getMessage()));
        }
    }
}
