package de.flowhive.server.routes;

import de.flowhive.server.IRoute;
import de.flowhive.server.Request;
import de.flowhive.server.RequestType;
import de.flowhive.server.response.APIResponse;
import io.javalin.http.Context;

@Request(type = RequestType.GET)
public class IndexRoute implements IRoute {

    @Override
    public void callback(Context ctx) {
        System.out.println("Debug Connection: " + ctx.fullUrl());
        ctx.status(200).json(new APIResponse(true, "The Webserver connection is stable!"));
    }

    @Override
    public String getPath() {
        return "/";
    }
}
