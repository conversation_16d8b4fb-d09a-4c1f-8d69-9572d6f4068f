package de.flowhive.server;

import de.flowhive.server.routes.IndexRoute;
import de.flowhive.server.routes.project.CreateProjectRoute;
import de.flowhive.server.routes.project.DeleteProjectRoute;
import de.flowhive.server.routes.project.UpdateProjectRoute;
import de.flowhive.server.routes.project.sprint.CreateSprintRoute;
import de.flowhive.server.routes.project.sprint.DeleteSprintRoute;
import de.flowhive.server.routes.project.sprint.UpdateSprintRoute;
import de.flowhive.server.routes.project.task.CreateTaskRoute;
import de.flowhive.server.routes.project.task.DeleteTaskRoute;
import de.flowhive.server.routes.project.task.UpdateTaskRoute;
import de.flowhive.server.routes.project.userstory.CreateUserStoryRoute;
import de.flowhive.server.routes.project.userstory.DeleteUserStoryRoute;
import de.flowhive.server.routes.project.userstory.UpdateUserStoryRoute;
import de.flowhive.server.routes.user.AuthenticationRoute;
import de.flowhive.server.routes.user.FetchGeneralRoute;
import de.flowhive.server.routes.user.LogoutRoute;
import de.flowhive.server.routes.user.RegisterRoute;
import de.flowhive.server.routes.user.UpdateUserRoute;
import de.flowhive.utils.HiveLog;
import io.javalin.Javalin;

import java.lang.annotation.Annotation;

public class Server {

    public static int PORT = 8080;
    private static Javalin serverInstance;

    public void createServer() {
        if (serverInstance != null) return;
        serverInstance = Javalin.create(config -> {
            config.bundledPlugins.enableCors(cors -> {
               cors.addRule(it -> {
                   it.allowCredentials = true;
                   it.allowHost("http://localhost:5173");
                   it.allowHost("https://flowhive.sigasoft.de");
                   it.exposeHeader("Authorization");
                   it.exposeHeader("Content-Type");
               });
            });
        }).start(PORT);

        // User Routes
        registerRoute(new IndexRoute());
        registerRoute(new RegisterRoute());
        registerRoute(new AuthenticationRoute());
        registerRoute(new LogoutRoute());
        registerRoute(new FetchGeneralRoute());
        registerRoute(new UpdateUserRoute());

        // Project Routes
        registerRoute(new CreateProjectRoute());
        registerRoute(new UpdateProjectRoute());
        registerRoute(new DeleteProjectRoute());

        // User Story Routes
        registerRoute(new CreateUserStoryRoute());
        registerRoute(new UpdateUserStoryRoute());
        registerRoute(new DeleteUserStoryRoute());

        // Task Routes
        registerRoute(new CreateTaskRoute());
        registerRoute(new UpdateTaskRoute());
        registerRoute(new DeleteTaskRoute());

        // Sprint Routes
        registerRoute(new CreateSprintRoute());
        registerRoute(new UpdateSprintRoute());
        registerRoute(new DeleteSprintRoute());
    }

    private void registerRoute(IRoute route) {
        for (Annotation ann : route.getClass().getAnnotations()) {
            if (ann.annotationType().equals(Request.class)) {
                Request request = (Request) ann;
                if (request.type() == RequestType.GET) {
                    serverInstance.get(route.getPath(), route::callback);
                    HiveLog.log(getClass(), "Registered GET route: " + route.getPath());
                } else {
                    serverInstance.post(route.getPath(), route::callback);
                    HiveLog.log(getClass(), "Registered POST route: " + route.getPath());
                }
                return;
            }
        }

        HiveLog.log(getClass(), "Failed to register route " + route.getPath());
    }

    public void shutdownGracefully() {

    }
}
