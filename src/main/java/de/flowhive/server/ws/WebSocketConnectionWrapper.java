package de.flowhive.server.ws;

import de.flowhive.user.User;
import io.javalin.websocket.WsConnectContext;

public class WebSocketConnectionWrapper {

    private WsConnectContext connection;
    private User user;

    public WebSocketConnectionWrapper(WsConnectContext connection) {
        this.connection = connection;
        this.user = user;
    }

    public WsConnectContext getConnection() {
        return connection;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }
}
