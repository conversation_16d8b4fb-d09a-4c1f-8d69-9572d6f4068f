package de.flowhive.server.ws;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import de.flowhive.FlowHive;
import de.flowhive.server.Server;
import de.flowhive.user.User;
import de.flowhive.utils.HiveLog;
import io.javalin.websocket.WsConfig;

public class WebSocketConnectionHandler {

    public WebSocketConnectionHandler(WsConfig ws) {
        ws.onConnect(ctx -> {
            Server.getConnections().add(new WebSocketConnectionWrapper(ctx));
            HiveLog.log(getClass(), "Client connected: " + ctx.sessionId());
        });
        ws.onMessage(ctx -> {
            HiveLog.log(getClass(), "Client sent message: " + ctx.message());
            JsonElement elem = JsonParser.parseString(ctx.message());
            if (!elem.isJsonObject()) {
                HiveLog.log(getClass(), "Client sent invalid message: " + ctx.message());
                return;
            }

            JsonObject obj = elem.getAsJsonObject();
            if (!obj.has("type")) {
                HiveLog.log(getClass(), "Client sent invalid message: " + ctx.message());
                return;
            }

            JsonObject data = obj.get("data").getAsJsonObject();
            WsMessageType type = WsMessageType.valueOf(obj.get("type").getAsString());
            switch (type) {
                case HANDSHAKE:
                    int userId = data.get("userId").getAsInt();
                    User user = FlowHive.getInstance().getUserManager().getUser(userId);
                    Server.getConnections().forEach(connection -> {
                        if (connection.getConnection().sessionId().equals(ctx.sessionId())) {
                            connection.setUser(user);
                        }
                    });
                    break;
                case REFRESH:
                    Server.getConnections().forEach(connection -> {
                        if (connection.getConnection().sessionId().equals(ctx.sessionId())) {
                            connection.getConnection().send("{\"type\": \"REFRESH\"}");
                        }
                    });
                    break;
            }
        });
        ws.onClose(ctx -> {
            HiveLog.log(getClass(), "Client disconnected: " + ctx.sessionId());
            Server.getConnections().forEach(connection -> {
                if (connection.getConnection().sessionId().equals(ctx.sessionId())) {
                    Server.getConnections().remove(connection);
                }
            });
        });
    }

}
