package de.flowhive;

import de.flowhive.project.PRIORITY;
import de.flowhive.project.ProjectManager;
import de.flowhive.notifications.NotificationManager;
import de.flowhive.project.TASKSTATUS;
import de.flowhive.project.attributes.Task;
import de.flowhive.server.Server;
import de.flowhive.timer.HiveBeat;
import de.flowhive.user.UserManager;
import de.flowhive.utils.HiveLog;

import java.util.Date;

public class FlowHive {

    public static boolean DEBUG = false;
    private static FlowHive instance;

    private Server server;
    private UserManager userManager;
    private ProjectManager projectManager;
    private NotificationManager notificationManager;
    private HiveBeat timer;

    /**
     * Gets an environment variable or system property.
     * First checks environment variables, then falls back to system properties.
     *
     * @param name The name of the variable/property
     * @return The value or null if not found
     */
    private String getEnvOrProperty(String name) {
        String value = System.getenv(name);
        if (value == null) {
            value = System.getProperty(name);
        }
        return value;
    }

    private void init() {
        // check if project runs from hosting or local environment
        String environment = getEnvOrProperty("ENVIRONMENT");
        if (environment == null) {
            HiveLog.log(getClass(), "No environment was detected");
            System.exit(0);
        }
        DEBUG = environment.equals("development");
        HiveLog.log(getClass(), "Detected " + (DEBUG ? "local" : "production") + " environment");

        // init subsystems
        userManager = new UserManager();
        projectManager = new ProjectManager();
        notificationManager = new NotificationManager();
        server = new Server();

        // post init
        server.createServer();
        timer = HiveBeat.getInstance().startTimer();
    }

    public static FlowHive getInstance() {
        // ensuring that FlowHive is a singleton class
        if (instance == null) {
            instance = new FlowHive();
            instance.init();
        }
        return instance;
    }

    public UserManager getUserManager() {
        return userManager;
    }

    public ProjectManager getProjectManager() {
        return projectManager;
    }

    public NotificationManager getNotificationManager() {
        return notificationManager;
    }

    public HiveBeat getTimer() {
        return timer;
    }

    public Server getServer() {
        return server;
    }

    public static void main(String[] args) {
        getInstance();
    }
}
