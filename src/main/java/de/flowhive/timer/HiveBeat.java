package de.flowhive.timer;


import de.flowhive.notifications.NotificationManager;
import de.flowhive.utils.HiveLog;

import java.sql.SQLException;
import java.util.Timer;
import java.util.TimerTask;

// We first need to check out how TimerTask ins java work again hehe...
public class HiveBeat {

    private static HiveBeat instance;
    private Timer timer;
    private boolean timerdown = false;

    private HiveBeat() {
        timer = new Timer();
        timerdown = true;
    }

    public static HiveBeat getInstance() {
        if (instance == null) {
            instance = new HiveBeat();
        }
        return instance;
    }

    public HiveBeat startTimer() {
        if(timerdown){
            timer = new Timer();
            timer.scheduleAtFixedRate(new TimerTask() {
                @Override
                public void run() {
                    //TODO in Classdiagramm private Void: Override does not seem to work with private void -> change in architecture
                    HiveLog.debug(HiveLog.class, "TICK");
                    try {
                        NotificationManager.checkDates();
                    } catch (SQLException e) {
                        HiveLog.log(HiveLog.class, e.getMessage());
                    }
                }
            }, 0, 300000); //interval 5min
            timerdown = false;
            HiveLog.log(HiveLog.class, "Timer started successfully!");
        } else {
            HiveLog.debug(HiveLog.class, "Tried to start timer but timer its already running.");
        }
        return this;
    }

    public void shutdownGracefully() {
        if (timer != null) {
            HiveLog.log(HiveLog.class, "Shutting down timer gracefully");
            timer.cancel();
            timerdown = true;
        } else {
            HiveLog.debug(getClass(), "Tried to shutdown timer but timer is not running!");
        }

    }
}
