# Flowhive-Backend

---
## Getting started

The backend of FlowHive is build on Java-Maven to dynamically add, remove and export libraries.
We will use JDK 21 during Development since it is the latest LTS Java version.

## Things todo before we can start to code ;)

- [X] Add Maven to Project
- [X] Create Project Skeleton
- [X] Setup Docker Workflow
- [X] Implement proper env access
- [X] Create a concept to create proper logs

Nice we completed all of them happy hacking :)

# Route Documentation
[User Routes](/documentation/routes/UserRoutes.md) - Here you can find all the information to register, authenticate, and retrieve or update comprehensive user data

[Project Routes](/documentation/routes/ProjectRoutes.md) - Information about project management endpoints

[User Story Routes](/documentation/routes/UserStoryRoutes.md) - Information about user story management endpoints

[Task Routes](/documentation/routes/TaskRoutes.md) - Information about task management endpoints

[Sprint Routes](/documentation/routes/SprintRoutes.md) - Information about sprint management endpoints

# Running Tests

## Setup

1. Copy the `.env.local.example` file to `.env.local` and adjust the values as needed:
   ```
   cp .env.local.example .env.local
   ```

2. Make sure you have a PostgreSQL database running with the credentials specified in your `.env.local` file.

## Running Tests
### Using Maven
To run a specific test class:

```
mvn test -Dtest=UserStoryTests
```

or

```
mvn test -Dtest=TaskTests
```