# FlowHive User API Documentation

This document explains how to use the user-related endpoints of the FlowHive API. There are five main endpoints described here: one for user registration, one for user authentication, one for user logout, one for updating user information, and one for retrieving comprehensive user data.

---

## Field Validations

For security and consistency, input fields are validated using strict rules. **Note:** The exact regular expressions are not shown here; instead, the validations are described in plain language.

### Password Requirements
- **Requirements:**
    - Must include **at least one lowercase letter**.
    - Must include **at least one uppercase letter**.
    - Must include **at least one digit**.
    - Must include **at least one special character** (from a defined set such as `@`, `$`, `#`, `!`, `%`, `+`, `_`, `*`, `?`, and `&`).
    - Must be between **8 and 64 characters** long.

  These rules ensure that passwords are strong and hard to guess.

### Username Requirements
- **Requirements:**
    - Can only contain **letters and digits**.
    - No spaces or special characters are allowed.

### Name Requirements (First Name and Surname)
- **Requirements:**
    - Can only include **letters and spaces**.

### Email Requirements
- **Requirements:**
    - The local part (before the `@`) can be up to **64 characters long**.
    - Both local and domain parts must use valid characters.
    - The domain must follow standard conventions and include a valid top-level domain.

---

## Endpoints

### 1. User Registration

- **Endpoint:** `/user/register`
- **Method:** `POST`

#### Request Body

The request must be a JSON object containing the following fields:

| Field     | Type   | Description                                               | Validation Summary                                       |
|-----------|--------|-----------------------------------------------------------|----------------------------------------------------------|
| `username`| String | The desired username for the new account.               | Only letters and digits are allowed.                   |
| `password`| String | The desired password for the new account.               | Must include uppercase, lowercase, digit, special character, and be 8–64 characters long. |
| `preName` | String | The user's first name.                                    | Only letters and spaces are allowed.                   |
| `surName` | String | The user's surname.                                       | Only letters and spaces are allowed.                   |
| `email`   | String | The user's email address.                                 | Must follow standard email formatting rules.           |

> **Note:**
> - All fields are required.
> - If the client is already authenticated (i.e., the request contains an authentication cookie), an error message will be returned stating that the user is already registered.

#### Example Request

```json
{
"username": "JohnDoe123",
"password": "Secure@Pass1",
"preName": "John",
"surName": "Doe",
"email": "<EMAIL>"
}
```

#### Response

- **Success Response (HTTP 200):**

```json
{
"success": true,
"message": "User registered successfully"
}
```

- **Error Responses (HTTP 400):**
    - For missing fields or malformed requests:

```json
{
"success": false,
"message": "Bad request"
}
```

- For registration failures due to business logic:

```jsons
{
"success": false,
"message": "Failed to register user."
}
```

---

### 2. User Authentication

- **Endpoint:** `/user/authenticate`
- **Method:** `POST`

#### Request Body

The request must be a JSON object containing the following fields:

| Field      | Type   | Description                   |
|------------|--------|-------------------------------|
| `username` | String | The username of the account.  |
| `password` | String | The password for the account. |

> **Note:**
> - If a client already has an authentication cookie (indicating an active session), the server will return an error stating that the user is already registered.

#### Example Request

```json
{
"username": "JohnDoe123",
"password": "Secure@Pass1"
}
```

#### Response

- **Success Response (HTTP 200):**
    - Upon successful authentication, the server creates an authentication cookie named `auth` containing a signed JWT token. This cookie is:
        - Set for the entire domain (path `/`).
        - Marked as `HttpOnly` to prevent client-side script access.
        - Configured to expire in 7 days.
    - JSON response:

```json
{
"success": true,
"message": "Authentication Successful"
}
```

- **Error Responses (HTTP 400):**
    - For malformed requests or missing fields:

```json
{
"success": false,
"message": "Bad request"
}
```

- For invalid credentials:

```json
{
"success": false,
"message": "Invalid credentials"
}
```

- If the user is already authenticated:

```json
{
"success": false,
"message": "You are already registered."
}
```

---

### 3. User Logout

- **Endpoint:** `/user/logout`
- **Method:** `POST`

This endpoint allows users to log out by clearing their authentication cookie. The cookie is set to an empty string and expires in 5 seconds.

#### Request

No request body is required for this endpoint. The authentication cookie will be cleared regardless of whether the user is currently authenticated.

#### Example Request

No request body needed - just send a POST request to the endpoint.

#### Response

- **Success Response (HTTP 200):**

```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

- **Error Responses:**
  - For server errors (HTTP 500):

```json
{
  "success": false,
  "message": "Error during logout: [error details]"
}
```

#### Notes

- This endpoint can be called even if the user is not currently authenticated.
- The authentication cookie is cleared by setting it to an empty string with a 5-second expiration time.
- After calling this endpoint, the user will need to authenticate again to access protected endpoints.

---

### 4. Update User Information

- **Endpoint:** `/user/update`
- **Method:** `POST`
- **Authentication:** Required

This endpoint allows authenticated users to update their profile information or change their password.

#### Request Body for Profile Update

The request must be a JSON object containing one or more of the following fields:

| Field     | Type   | Description                                               | Validation Summary                                       |
|-----------|--------|-----------------------------------------------------------|----------------------------------------------------------|
| `username`| String | The new username for the account.                        | Only letters and digits are allowed.                   |
| `preName` | String | The user's new first name.                                | Only letters and spaces are allowed.                   |
| `surName` | String | The user's new surname.                                   | Only letters and spaces are allowed.                   |
| `email`   | String | The user's new email address.                             | Must follow standard email formatting rules.           |

> **Note:**
> - At least one field must be provided.
> - Only the fields that are provided will be updated.

#### Example Request for Profile Update

```json
{
  "username": "JohnDoe456",
  "preName": "Johnny",
  "surName": "Doe",
  "email": "<EMAIL>"
}
```

#### Request Body for Password Change

The request must be a JSON object containing the following fields:

| Field         | Type   | Description                                               | Validation Summary                                       |
|---------------|--------|-----------------------------------------------------------|----------------------------------------------------------|
| `oldPassword` | String | The current password for the account.                    | Must match the current password.                       |
| `newPassword` | String | The new password for the account.                        | Must include uppercase, lowercase, digit, special character, and be 8–64 characters long. |

> **Note:**
> - Both fields are required for password changes.
> - The new password must be different from the old password.

#### Example Request for Password Change

```json
{
  "oldPassword": "Secure@Pass1",
  "newPassword": "EvenMore@Secure2"
}
```

#### Response for Profile Update

- **Success Response (HTTP 200):**

```json
{
  "success": true,
  "message": "User information updated successfully",
  "data": {
    "id": 123,
    "username": "JohnDoe456",
    "preName": "Johnny",
    "surName": "Doe",
    "email": "<EMAIL>"
  }
}
```

- **Error Responses:**
  - For missing fields or malformed requests (HTTP 400):

```json
{
  "success": false,
  "message": "No fields to update were provided"
}
```

  - For validation failures (HTTP 400):

```json
{
  "success": false,
  "message": "Failed to update user information. Please check that all fields meet the requirements."
}
```

  - For authentication failures (HTTP 401):

```json
{
  "success": false,
  "message": "Not signed in."
}
```

#### Response for Password Change

- **Success Response (HTTP 200):**

```json
{
  "success": true,
  "message": "Password updated successfully"
}
```

- **Error Responses:**
  - For incorrect old password or invalid new password (HTTP 400):

```json
{
  "success": false,
  "message": "Failed to update password. Please check that your old password is correct and the new password meets the requirements."
}
```

  - For same old and new password (HTTP 400):

```json
{
  "success": false,
  "message": "New password must be different from the old password"
}
```

  - For authentication failures (HTTP 401):

```json
{
  "success": false,
  "message": "Not signed in."
}
```

---

### 5. Fetch User General Data

- **Endpoint:** `/user/general`
- **Method:** `GET`
- **Authentication:** Required

This endpoint allows authenticated users to retrieve their own data along with all related projects, user stories, tasks, sprints, and project members. This provides a comprehensive view of all data relevant to the user.

#### Request

No request body is required for this endpoint. The user is identified by the JWT token in the `auth` cookie.

#### Response

- **Success Response (HTTP 200):**

```json
{
  "success": true,
  "message": "User data retrieved successfully",
  "data": {
    "user": {
      "id": 123,
      "username": "johndoe",
      "preName": "John",
      "surName": "Doe",
      "mail": "<EMAIL>",
      "inbox": []
    },
    "projects": [
      {
        "id": 456,
        "name": "Project 1",
        "standards": "Project standards and guidelines...",
        "productOwner": 123,
        "members": [123, 789, 101],
        "role": "PRODUCT_OWNER",
        "userStories": [
          {
            "id": 202,
            "name": "User Story 1",
            "description": "Description of user story 1",
            "priority": "MEDIUM",
            "tasks": [
              {
                "id": 303,
                "name": "Task 1",
                "description": "Description of task 1",
                "priority": "HIGH",
                "status": "IN_PROGRESS",
                "startDate": "2023-06-01",
                "dueDate": "2023-06-15",
                "estimatedWorkTime": 8,
                "actualWorkTime": 6,
                "assignedTo": [123, 789]
              }
            ]
          }
        ],
        "sprints": [
          {
            "id": 505,
            "name": "Sprint 1",
            "description": "Description of sprint 1",
            "startDate": "2023-06-01T00:00:00.000+00:00",
            "dueDate": "2023-06-15T00:00:00.000+00:00",
            "userStoryIds": [202, 203]
          }
        ]
      },
      {
        "id": 457,
        "name": "Project 2",
        "standards": "Project standards and guidelines...",
        "productOwner": 789,
        "members": [123, 789],
        "role": "MEMBER",
        "userStories": [
          {
            "id": 204,
            "name": "User Story 2",
            "description": "Description of user story 2",
            "priority": "HIGH",
            "tasks": [
              {
                "id": 305,
                "name": "Task 2",
                "description": "Description of task 2",
                "priority": "MEDIUM",
                "status": "NOT_STARTED",
                "startDate": "2023-06-10",
                "dueDate": "2023-06-20",
                "estimatedWorkTime": 16,
                "actualWorkTime": 0,
                "assignedTo": [123]
              }
            ]
          }
        ],
        "sprints": []
      }
    ],
    "members": [
      {
        "id": 123,
        "username": "johndoe",
        "preName": "John",
        "surName": "Doe",
        "mail": "<EMAIL>"
      },
      {
        "id": 789,
        "username": "janedoe",
        "preName": "Jane",
        "surName": "Doe",
        "mail": "<EMAIL>"
      },
      {
        "id": 101,
        "username": "bobsmith",
        "preName": "Bob",
        "surName": "Smith",
        "mail": "<EMAIL>"
      }
    ]
  }
}
```

- **Error Responses:**
  - For authentication failures (HTTP 401):

```json
{
  "success": false,
  "message": "Not signed in."
}
```

  - For user not found (HTTP 404):

```json
{
  "success": false,
  "message": "User not found."
}
```

  - For server errors (HTTP 500):

```json
{
  "success": false,
  "message": "Error fetching user data: [error details]"
}
```

#### Response Structure

The response data contains three main sections:

##### User Section

The `user` section contains basic information about the authenticated user:

| Field       | Type   | Description                                |
|-------------|--------|-----------------------------------------|
| `id`        | Integer| The unique identifier for the user         |
| `username`  | String | The user's username                        |
| `preName`   | String | The user's first name                      |
| `surName`   | String | The user's last name                       |
| `email`     | String | The user's email address                   |
| `inbox`     | Array  | List of notifications for the user         |

##### Projects Section

The `projects` section is an array of project objects. Each project object contains:

| Field         | Type   | Description                                |
|---------------|--------|-----------------------------------------|
| `id`          | Integer| The unique identifier for the project      |
| `name`        | String | The name of the project                    |
| `standards`   | String | Project standards and guidelines           |
| `productOwner`| Integer| The ID of the project's product owner      |
| `members`     | Array  | List of user IDs who are members of the project |
| `role`        | String | The user's role in the project (PRODUCT_OWNER or MEMBER) |
| `userStories` | Array  | List of user stories in the project        |
| `sprints`     | Array  | List of sprints in the project             |

##### Members Section

The `members` section is an array of user objects representing all members from the user's projects:

| Field       | Type   | Description                                |
|-------------|--------|-----------------------------------------|
| `id`        | Integer| The unique identifier for the user         |
| `username`  | String | The user's username                        |
| `preName`   | String | The user's first name                      |
| `surName`   | String | The user's last name                       |
| `email`     | String | The user's email address                   |
| Other user properties may also be included                       |

###### User Stories

Each user story object in the `userStories` array contains:

| Field         | Type   | Description                                |
|---------------|--------|-----------------------------------------|
| `id`          | Integer| The unique identifier for the user story   |
| `name`        | String | The name of the user story                 |
| `description` | String | The description of the user story          |
| `priority`    | String | The priority level (HIGH, MEDIUM, LOW)     |
| `tasks`       | Array  | List of tasks associated with the user story |

###### Tasks

Each task object in the `tasks` array contains:

| Field              | Type   | Description                                |
|--------------------|--------|-----------------------------------------|
| `id`               | Integer| The unique identifier for the task         |
| `name`             | String | The name of the task                       |
| `description`      | String | The description of the task                |
| `priority`         | String | The priority level (HIGH, MEDIUM, LOW)     |
| `status`           | String | The current status of the task             |
| `startDate`        | String | The start date of the task (if set)        |
| `dueDate`          | String | The due date of the task (if set)          |
| `estimatedWorkTime`| Integer| The estimated work time in hours           |
| `actualWorkTime`   | Integer| The actual work time spent on the task     |
| `assignedTo`       | Array  | List of user IDs assigned to the task      |

###### Sprints

Each sprint object in the `sprints` array contains:

| Field         | Type   | Description                                |
|---------------|--------|-----------------------------------------|
| `id`          | Integer| The unique identifier for the sprint       |
| `name`        | String | The name of the sprint                     |
| `description` | String | The description of the sprint              |
| `startDate`   | String | The start date of the sprint (ISO format, null if not set) |
| `dueDate`     | String | The due date of the sprint (ISO format, null if not set)   |
| `userStoryIds`| Array  | List of user story IDs assigned to the sprint |

#### Notes

- For tasks, only those assigned to the authenticated user or tasks in projects where the user is the product owner will be included in the response.
- Sprint dates (`startDate` and `dueDate`) are returned in ISO format when set, or as `null` if no dates have been assigned to the sprint.
- The `members` section includes all users who are members of any project the authenticated user is part of, providing easy access to member information without additional API calls.
- The response provides a complete hierarchical view of all data related to the user, making it efficient for client applications to load all necessary data in a single request.
- This endpoint is particularly useful for initializing client-side applications with all the user's data.

---

## Additional Notes

- **JWT Authentication Cookie:**
  On successful authentication, the server generates a JWT (JSON Web Token) that includes the user's ID and username. This token is signed securely using the HS256 algorithm and stored in an `HttpOnly` cookie, making it inaccessible to JavaScript in the browser.

- **Logout Functionality:**
  The logout endpoint clears the authentication cookie by setting it to an empty string with a 5-second expiration time. This ensures that the user is immediately logged out and the cookie is removed from the browser.

- **Input Validation:**
  The API strictly validates all inputs based on the criteria described above to ensure security and data integrity.

- **Error Handling:**
  All endpoints return clear error messages and appropriate HTTP status codes (400 for client errors and 500 for server errors) in case of issues with the request or processing.

---

This documentation covers the primary endpoints for user management in the FlowHive API. For further details on other endpoints or additional functionality, please refer to the complete API reference.
