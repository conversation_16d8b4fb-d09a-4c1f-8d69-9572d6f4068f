# FlowHive Project API Documentation

This document explains how to use the project-related endpoints of the FlowHive API. There are three main endpoints described here: one for creating projects, one for updating projects (including team members), and one for deleting projects.

---

## Authentication Requirements

All project-related endpoints require authentication. Requests must include a valid JWT token in an `auth` cookie, which is obtained through the authentication endpoint (`/user/authenticate`).

If a request is made without authentication, the server will respond with:

```json
{
  "success": false,
  "message": "Not signed in."
}
```

---

## Endpoints

### 1. Create Project

- **Endpoint:** `/project/create`
- **Method:** `POST`
- **Authentication:** Required

This endpoint allows authenticated users to create a new project. The user who creates the project automatically becomes its product owner.

#### Request Body

The request must be a JSON object containing the following fields:

| Field       | Type   | Description                    | Validation Summary                |
|-------------|--------|--------------------------------|-----------------------------------|
| `name`      | String | The name of the new project.   | Required, cannot be empty.        |
| `standards` | String | Project standards/guidelines.  | Optional.                         |

#### Example Request

```json
{
  "name": "New Project Name",
  "standards": "These are the project standards and guidelines..."
}
```

#### Response

- **Success Response (HTTP 201):**

```json
{
  "success": true,
  "message": "Project created successfully",
  "data": {
    "id": 123,
    "name": "New Project Name",
    "standards": "These are the project standards and guidelines...",
    "productOwner": 456,
    "members": [],
    "backlog": [],
    "sprints": []
  }
}
```

- **Error Responses:**
  - For missing fields or malformed requests (HTTP 400):

```json
{
  "success": false,
  "message": "Bad request: name is required"
}
```

  - For authentication failures (HTTP 401):

```json
{
  "success": false,
  "message": "Not signed in."
}
```

---

### 2. Update Project

- **Endpoint:** `/project/update`
- **Method:** `POST`
- **Authentication:** Required

This endpoint allows the product owner of a project to update project details including name, standards, and team members. The request can include any combination of these fields.

#### Request Body

The request must be a JSON object containing the following fields:

| Field           | Type    | Description                                      | Validation Summary                |
|-----------------|---------|--------------------------------------------------|-----------------------------------|
| `projectId`     | Integer | The ID of the project to update.                 | Required, must be a valid ID.     |
| `name`          | String  | The new name for the project.                    | Optional.                         |
| `standards`     | String  | The new standards/guidelines for the project.    | Optional.                         |
| `removeMembers` | Array   | Array of user IDs to remove from the project.    | Optional, can be an empty array.  |
| `inviteMembers` | Array   | Array of usernames to invite to the project.     | Optional, can be an empty array.  |

#### Example Request

```json
{
  "projectId": 123,
  "name": "Updated Project Name",
  "standards": "Updated project standards and guidelines...",
  "removeMembers": [456],
  "inviteMembers": ["johndoe", "janedoe"]
}
```

#### Response

- **Success Response (HTTP 200):**

```json
{
  "success": true,
  "message": "Project details and team members updated successfully",
  "data": {
    "id": 123,
    "name": "Updated Project Name",
    "standards": "Updated project standards and guidelines...",
    "productOwner": 456,
    "members": [456, 789, 101],
    "backlog": [],
    "sprints": []
  }
}
```

- **Error Responses:**
  - For missing fields or malformed requests (HTTP 400):

```json
{
  "success": false,
  "message": "Missing required field: projectId"
}
```

  - For invalid ID formats (HTTP 400):

```json
{
  "success": false,
  "message": "Invalid project ID or member ID format"
}
```

  - For authentication failures (HTTP 401):

```json
{
  "success": false,
  "message": "Not signed in."
}
```

  - For permission issues (HTTP 403):

```json
{
  "success": false,
  "message": "You don't have permission to modify this project"
}
```

  - For non-existent projects (HTTP 404):

```json
{
  "success": false,
  "message": "Project not found"
}
```

  - For server errors (HTTP 500):

```json
{
  "success": false,
  "message": "Error updating project: [error details]"
}
```

### 3. Delete Project

- **Endpoint:** `/project/delete`
- **Method:** `POST`
- **Authentication:** Required

This endpoint allows the product owner of a project to delete the project. This operation cannot be undone.

#### Request Body

The request must be a JSON object containing the following fields:

| Field       | Type    | Description                                      | Validation Summary                |
|-------------|---------|--------------------------------------------------|-----------------------------------|
| `projectId` | Integer | The ID of the project to delete.                 | Required, must be a valid ID.     |

#### Example Request

```json
{
  "projectId": 123
}
```

#### Response

- **Success Response (HTTP 200):**

```json
{
  "success": true,
  "message": "Project deleted successfully"
}
```

- **Error Responses:**
  - For missing fields or malformed requests (HTTP 400):

```json
{
  "success": false,
  "message": "Missing required field: projectId"
}
```

  - For invalid ID formats (HTTP 400):

```json
{
  "success": false,
  "message": "Invalid project ID format"
}
```

  - For authentication failures (HTTP 401):

```json
{
  "success": false,
  "message": "Not signed in."
}
```

  - For permission issues (HTTP 403):

```json
{
  "success": false,
  "message": "You don't have permission to delete this project"
}
```

  - For non-existent projects (HTTP 404):

```json
{
  "success": false,
  "message": "Project not found"
}
```

  - For server errors (HTTP 500):

```json
{
  "success": false,
  "message": "Error deleting project: [error details]"
}
```

---

## Additional Notes

- **Project Ownership:**
  The user who creates a project automatically becomes its product owner. Only the product owner can modify the project details, team members, or delete the project.

- **Team Members:**
  When removing members, they are specified by their user IDs. When inviting new members, they are specified by their usernames. The API validates that usernames correspond to existing users before adding them to the project.

- **Incremental Member Management:**
  The update endpoint now supports incremental member management. You can remove specific members using the `removeMembers` array and invite new members using the `inviteMembers` array. This allows for more granular control over project membership without needing to retrieve and modify the complete list.

- **Partial Updates:**
  The update endpoint supports partial updates. You can include only the fields you want to update in the request body. For example, if you only want to update the project name, you can include only the `projectId` and `name` fields.

- **Error Handling:**
  All endpoints return clear error messages and appropriate HTTP status codes in case of issues with the request or processing.

---

This documentation covers the primary endpoints for project management in the FlowHive API. For further details on other endpoints or additional functionality, please refer to the complete API reference.
