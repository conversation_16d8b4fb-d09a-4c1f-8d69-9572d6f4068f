# FlowHive User Story API Documentation

This document explains how to use the user story-related endpoints of the FlowHive API. There are three main endpoints described here: one for creating user stories, one for updating user stories, and one for deleting user stories.

---

## Authentication Requirements

All user story-related endpoints require authentication. Requests must include a valid JWT token in an `auth` cookie, which is obtained through the authentication endpoint (`/user/authenticate`).

If a request is made without authentication, the server will respond with:

```json
{
  "success": false,
  "message": "Not signed in."
}
```

---

## Endpoints

### 1. Create User Story

- **Endpoint:** `/project/userstory/create`
- **Method:** `POST`
- **Authentication:** Required

This endpoint allows authenticated users to create a new user story for a specific project.

#### Request Body

The request must be a JSON object containing the following fields:

| Field         | Type    | Description                                      | Validation Summary                |
|---------------|---------|--------------------------------------------------|-----------------------------------|
| `projectId`   | Integer | The ID of the project this user story belongs to. | Required, must be a valid ID.     |
| `name`        | String  | The name of the user story.                      | Required, cannot be empty.        |
| `description` | String  | The description of the user story.               | Required, cannot be empty.        |
| `priority`    | String  | The priority level of the user story.            | Optional, defaults to "MEDIUM". Valid values: "HIGH", "MEDIUM", "LOW". |

#### Example Request

```json
{
  "projectId": 123,
  "name": "User Registration",
  "description": "As a new user, I want to register an account so that I can access the system",
  "priority": "HIGH"
}
```

#### Response

- **Success Response (HTTP 201):**

```json
{
  "success": true,
  "message": "User story created successfully",
  "data": {
    "id": 456,
    "name": "User Registration",
    "description": "As a new user, I want to register an account so that I can access the system",
    "priority": "HIGH"
  }
}
```

- **Error Responses:**
  - For missing fields or malformed requests (HTTP 400):

```json
{
  "success": false,
  "message": "Missing required fields: projectId, name, description"
}
```

  - For invalid values (HTTP 400):

```json
{
  "success": false,
  "message": "Invalid priority value. Valid values are: HIGH, MEDIUM, LOW"
}
```

  - For authentication failures (HTTP 401):

```json
{
  "success": false,
  "message": "Not signed in."
}
```

  - For permission issues (HTTP 403):

```json
{
  "success": false,
  "message": "You don't have permission to create user stories for this project"
}
```

  - For non-existent projects (HTTP 404):

```json
{
  "success": false,
  "message": "Project not found"
}
```

  - For server errors (HTTP 500):

```json
{
  "success": false,
  "message": "Error creating user story: [error details]"
}
```

---

### 2. Update User Story

- **Endpoint:** `/project/userstory/update`
- **Method:** `POST`
- **Authentication:** Required

This endpoint allows authenticated users to update an existing user story. The request can include any combination of fields to update.

#### Request Body

The request must be a JSON object containing the following fields:

| Field         | Type    | Description                                      | Validation Summary                |
|---------------|---------|--------------------------------------------------|-----------------------------------|
| `userStoryId` | Integer | The ID of the user story to update.              | Required, must be a valid ID.     |
| `name`        | String  | The new name for the user story.                 | Optional.                         |
| `description` | String  | The new description for the user story.          | Optional.                         |
| `priority`    | String  | The new priority level for the user story.       | Optional. Valid values: "HIGH", "MEDIUM", "LOW". |

#### Example Request

```json
{
  "userStoryId": 456,
  "name": "User Registration and Login",
  "priority": "MEDIUM"
}
```

#### Response

- **Success Response (HTTP 200):**

```json
{
  "success": true,
  "message": "User story updated successfully",
  "data": {
    "id": 456,
    "name": "User Registration and Login",
    "description": "As a new user, I want to register an account so that I can access the system",
    "priority": "MEDIUM"
  }
}
```

- **Error Responses:**
  - For missing fields or malformed requests (HTTP 400):

```json
{
  "success": false,
  "message": "Missing required field: userStoryId"
}
```

  - For invalid values (HTTP 400):

```json
{
  "success": false,
  "message": "Invalid priority value. Valid values are: HIGH, MEDIUM, LOW"
}
```

  - For authentication failures (HTTP 401):

```json
{
  "success": false,
  "message": "Not signed in."
}
```

  - For permission issues (HTTP 403):

```json
{
  "success": false,
  "message": "You don't have permission to update user stories for this project"
}
```

  - For non-existent user stories (HTTP 404):

```json
{
  "success": false,
  "message": "User story not found"
}
```

  - For server errors (HTTP 500):

```json
{
  "success": false,
  "message": "Error updating user story: [error details]"
}
```

---

### 3. Delete User Story

- **Endpoint:** `/project/userstory/delete`
- **Method:** `POST`
- **Authentication:** Required

This endpoint allows the product owner to delete an existing user story.

#### Request Body

The request must be a JSON object containing the following fields:

| Field         | Type    | Description                                      | Validation Summary                |
|---------------|---------|--------------------------------------------------|-----------------------------------|
| `userStoryId` | Integer | The ID of the user story to delete.              | Required, must be a valid ID.     |

#### Example Request

```json
{
  "userStoryId": 456
}
```

#### Response

- **Success Response (HTTP 200):**

```json
{
  "success": true,
  "message": "User story deleted successfully"
}
```

- **Error Responses:**
  - For missing fields or malformed requests (HTTP 400):

```json
{
  "success": false,
  "message": "Missing required field: userStoryId"
}
```

  - For invalid ID formats (HTTP 400):

```json
{
  "success": false,
  "message": "Invalid user story ID format"
}
```

  - For authentication failures (HTTP 401):

```json
{
  "success": false,
  "message": "Not signed in."
}
```

  - For permission issues (HTTP 403):

```json
{
  "success": false,
  "message": "Only the product owner can delete user stories"
}
```

  - For non-existent user stories (HTTP 404):

```json
{
  "success": false,
  "message": "User story not found"
}
```

  - For server errors (HTTP 500):

```json
{
  "success": false,
  "message": "Error deleting user story: [error details]"
}
```

---

## Additional Notes

- **User Story Ownership:**  
  User stories belong to projects. Any project member can create and update user stories, but only the product owner can delete them.

- **Partial Updates:**  
  The update endpoint supports partial updates. You can include only the fields you want to update in the request body. For example, if you only want to update the user story name, you can include only the `userStoryId` and `name` fields.

- **Error Handling:**  
  All endpoints return clear error messages and appropriate HTTP status codes in case of issues with the request or processing.

---

This documentation covers the primary endpoints for user story management in the FlowHive API. For further details on other endpoints or additional functionality, please refer to the complete API reference.
