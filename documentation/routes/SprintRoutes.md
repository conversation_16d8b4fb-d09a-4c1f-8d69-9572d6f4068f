# FlowHive Sprint API Documentation

This document explains how to use the sprint-related endpoints of the FlowHive API. There are three main endpoints described here: one for creating sprints, one for updating sprints, and one for deleting sprints.

---

## Authentication Requirements

All sprint-related endpoints require authentication. Requests must include a valid JWT token in an `auth` cookie, which is obtained through the authentication endpoint (`/user/authenticate`).

If a request is made without authentication, the server will respond with:

```json
{
  "success": false,
  "message": "Not signed in."
}
```

---

## Endpoints

### 1. Create Sprint

- **Endpoint:** `/project/sprint/create`
- **Method:** `POST`
- **Authentication:** Required

This endpoint allows authenticated users to create a new sprint for a project. The user must be either the product owner or a team member of the project.

#### Request Body

The request must be a JSON object containing the following fields:

| Field         | Type    | Description                                | Validation Summary                |
|---------------|---------|--------------------------------------------|------------------------------------|
| `projectId`   | Integer | The ID of the project for this sprint.     | Required, must be a valid ID.      |
| `name`        | String  | The name of the new sprint.                | Required, cannot be empty.         |
| `description` | String  | The description of the sprint.             | Required.                          |
| `startDate`   | String  | The start date of the sprint (yyyy-MM-dd). | Optional, ISO date format.         |
| `dueDate`     | String  | The due date of the sprint (yyyy-MM-dd).   | Optional, ISO date format.         |

#### Example Request

```json
{
  "projectId": 123,
  "name": "Sprint 1",
  "description": "First sprint of the project focusing on core features.",
  "startDate": "2024-01-15",
  "dueDate": "2024-01-29"
}
```

**Note:** The `startDate` and `dueDate` fields are optional. If not provided, the sprint will be created without specific dates.

#### Response

- **Success Response (HTTP 201):**

```json
{
  "success": true,
  "message": "Sprint created successfully",
  "data": {
    "id": 456,
    "name": "Sprint 1",
    "description": "First sprint of the project focusing on core features.",
    "startDate": "2024-01-15T00:00:00.000+00:00",
    "dueDate": "2024-01-29T00:00:00.000+00:00",
    "userStories": []
  }
}
```

- **Error Responses:**
  - For missing fields or malformed requests (HTTP 400):

```json
{
  "success": false,
  "message": "Missing required fields: projectId, name, description"
}
```

  - For authentication failures (HTTP 401):

```json
{
  "success": false,
  "message": "Not signed in."
}
```

  - For permission issues (HTTP 403):

```json
{
  "success": false,
  "message": "You don't have permission to create sprints for this project"
}
```

  - For invalid project ID (HTTP 404):

```json
{
  "success": false,
  "message": "Project not found"
}
```

---

### 2. Update Sprint

- **Endpoint:** `/project/sprint/update`
- **Method:** `POST`
- **Authentication:** Required

This endpoint allows users to update an existing sprint's details and manage the user stories assigned to it. The user must be either the product owner or a team member of the project.

#### Request Body

The request must be a JSON object containing the following fields:

| Field               | Type           | Description                                                | Validation Summary                |
|---------------------|----------------|------------------------------------------------------------|-----------------------------------|
| `sprintId`          | Integer        | The ID of the sprint to update.                            | Required, must be a valid ID.     |
| `name`              | String         | The new name for the sprint.                               | Optional.                         |
| `description`       | String         | The new description for the sprint.                        | Optional.                         |
| `userStoryIds`      | Array<Integer> | Array of user story IDs to add to the sprint.              | Optional.                         |
| `removeUserStoryIds`| Array<Integer> | Array of user story IDs to remove from the sprint.         | Optional.                         |
| `startDate`         | String         | The start date of the sprint (yyyy-MM-dd).                  | Optional, ISO date format.        |
| `dueDate`           | String         | The due date of the sprint (yyyy-MM-dd).                    | Optional, ISO date format.        |

#### Example Request

```json
{
  "sprintId": 456,
  "name": "Updated Sprint 1",
  "description": "Updated description for the first sprint.",
  "userStoryIds": [789, 101],
  "removeUserStoryIds": [112],
  "startDate": "2024-01-20",
  "dueDate": "2024-02-03"
}
```

**Note:** All fields except `sprintId` are optional. You can update any combination of fields. To clear a date, set it to `null`.

#### Response

- **Success Response (HTTP 200):**

```json
{
  "success": true,
  "message": "Sprint details and user stories updated successfully",
  "data": {
    "id": 456,
    "name": "Updated Sprint 1",
    "description": "Updated description for the first sprint.",
    "startDate": "2024-01-20T00:00:00.000+00:00",
    "dueDate": "2024-02-03T00:00:00.000+00:00",
    "userStories": [
      {
        "id": 789,
        "name": "User Story 1",
        "description": "Description of user story 1",
        "priority": "MEDIUM"
      },
      {
        "id": 101,
        "name": "User Story 2",
        "description": "Description of user story 2",
        "priority": "HIGH"
      }
    ]
  }
}
```

- **Error Responses:**
  - For missing fields or malformed requests (HTTP 400):

```json
{
  "success": false,
  "message": "Missing required field: sprintId"
}
```

  - For authentication failures (HTTP 401):

```json
{
  "success": false,
  "message": "Not signed in."
}
```

  - For permission issues (HTTP 403):

```json
{
  "success": false,
  "message": "You don't have permission to update sprints for this project"
}
```

  - For invalid sprint ID (HTTP 404):

```json
{
  "success": false,
  "message": "Sprint not found"
}
```

---

### 3. Delete Sprint

- **Endpoint:** `/project/sprint/delete`
- **Method:** `POST`
- **Authentication:** Required

This endpoint allows users to delete a sprint. The user must be either the product owner or a team member of the project. This operation cannot be undone.

#### Request Body

The request must be a JSON object containing the following fields:

| Field       | Type    | Description                                      | Validation Summary                |
|-------------|---------|--------------------------------------------------|-----------------------------------|
| `sprintId`  | Integer | The ID of the sprint to delete.                  | Required, must be a valid ID.     |

#### Example Request

```json
{
  "sprintId": 456
}
```

#### Response

- **Success Response (HTTP 200):**

```json
{
  "success": true,
  "message": "Sprint deleted successfully"
}
```

- **Error Responses:**
  - For missing fields or malformed requests (HTTP 400):

```json
{
  "success": false,
  "message": "Missing required field: sprintId"
}
```

  - For authentication failures (HTTP 401):

```json
{
  "success": false,
  "message": "Not signed in."
}
```

  - For permission issues (HTTP 403):

```json
{
  "success": false,
  "message": "You don't have permission to delete sprints for this project"
}
```

  - For invalid sprint ID (HTTP 404):

```json
{
  "success": false,
  "message": "Sprint not found"
}
```

  - For server errors (HTTP 500):

```json
{
  "success": false,
  "message": "Error deleting sprint: [error details]"
}
```

---

## Additional Notes

- **Sprint Management:**
  Sprints are a way to organize user stories into time-boxed iterations. Each sprint belongs to a specific project.

- **User Story Assignment:**
  User stories can be assigned to a sprint using the update endpoint. You can add multiple user stories at once by providing their IDs in the `userStoryIds` array.

- **User Story Removal:**
  User stories can be removed from a sprint using the update endpoint. You can remove multiple user stories at once by providing their IDs in the `removeUserStoryIds` array.

- **Date Management:**
  Sprints can have optional start and due dates. Dates should be provided in ISO format (yyyy-MM-dd). To clear a date when updating, set the field to `null`. If dates are not provided during creation, the sprint will be created without specific dates.

- **Permissions:**
  Both the product owner and team members of a project can create, update, and delete sprints for that project.

- **Error Handling:**
  All endpoints return clear error messages and appropriate HTTP status codes in case of issues with the request or processing.

---

This documentation covers the primary endpoints for sprint management in the FlowHive API. For further details on other endpoints or additional functionality, please refer to the complete API reference.
