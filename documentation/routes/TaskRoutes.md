# FlowHive Task API Documentation

This document explains how to use the task-related endpoints of the FlowHive API. There are three main endpoints described here: one for creating tasks, one for updating tasks, and one for deleting tasks.

---

## Authentication Requirements

All task-related endpoints require authentication. Requests must include a valid JWT token in an `auth` cookie, which is obtained through the authentication endpoint (`/user/authenticate`).

If a request is made without authentication, the server will respond with:

```json
{
  "success": false,
  "message": "Not signed in."
}
```

---

## Endpoints

### 1. Create Task

- **Endpoint:** `/project/task/create`
- **Method:** `POST`
- **Authentication:** Required

This endpoint allows authenticated users to create a new task for a specific user story.

#### Request Body

The request must be a JSON object containing the following fields:

| Field              | Type    | Description                                      | Validation Summary                |
|--------------------|---------|--------------------------------------------------|-----------------------------------|
| `userStoryId`      | Integer | The ID of the user story this task belongs to.   | Required, must be a valid ID.     |
| `name`             | String  | The name of the task.                            | Required, cannot be empty.        |
| `description`      | String  | The description of the task.                     | Required, cannot be empty.        |
| `priority`         | String  | The priority level of the task.                  | Optional, defaults to "MEDIUM". Valid values: "HIGH", "MEDIUM", "LOW". |
| `status`           | String  | The current status of the task.                  | Optional, defaults to "NOT_STARTED". Valid values: "NOT_STARTED", "IN_PROGRESS", "UNDER_REVIEW", "TESTING", "DONE". |
| `startDate`        | String  | The start date for the task.                     | Optional, format: "yyyy-MM-dd".   |
| `dueDate`          | String  | The due date for the task.                       | Optional, format: "yyyy-MM-dd".   |
| `estimatedWorkTime`| Integer | The estimated work time in hours.                | Optional.                         |

#### Example Request

```json
{
  "userStoryId": 123,
  "name": "Implement login functionality",
  "description": "Create the login form and handle authentication",
  "priority": "HIGH",
  "status": "NOT_STARTED",
  "startDate": "2023-06-01",
  "dueDate": "2023-06-15",
  "estimatedWorkTime": 8
}
```

#### Response

- **Success Response (HTTP 201):**

```json
{
  "success": true,
  "message": "Task created successfully",
  "data": {
    "id": 456,
    "name": "Implement login functionality",
    "description": "Create the login form and handle authentication",
    "priority": "HIGH",
    "status": "NOT_STARTED",
    "starDate": "2023-06-01",
    "dueDate": "2023-06-15",
    "estimatedWorkTime": 8,
    "actualWorkTime": 0,
    "assignedTo": []
  }
}
```

- **Error Responses:**
  - For missing fields or malformed requests (HTTP 400):

```json
{
  "success": false,
  "message": "Missing required fields: userStoryId, name, description"
}
```

  - For invalid values (HTTP 400):

```json
{
  "success": false,
  "message": "Invalid priority value. Valid values are: HIGH, MEDIUM, LOW"
}
```

  - For authentication failures (HTTP 401):

```json
{
  "success": false,
  "message": "Not signed in."
}
```

  - For server errors (HTTP 500):

```json
{
  "success": false,
  "message": "Error creating task: [error details]"
}
```

---

### 2. Update Task

- **Endpoint:** `/project/task/update`
- **Method:** `POST`
- **Authentication:** Required

This endpoint allows authenticated users to update an existing task. The request can include any combination of fields to update.

#### Request Body

The request must be a JSON object containing the following fields:

| Field              | Type    | Description                                      | Validation Summary                |
|--------------------|---------|--------------------------------------------------|-----------------------------------|
| `taskId`           | Integer | The ID of the task to update.                    | Required, must be a valid ID.     |
| `name`             | String  | The new name for the task.                       | Optional.                         |
| `description`      | String  | The new description for the task.                | Optional.                         |
| `priority`         | String  | The new priority level for the task.             | Optional. Valid values: "HIGH", "MEDIUM", "LOW". |
| `status`           | String  | The new status for the task.                     | Optional. Valid values: "NOT_STARTED", "IN_PROGRESS", "UNDER_REVIEW", "TESTING", "DONE". |
| `startDate`        | String  | The new start date for the task.                 | Optional, format: "yyyy-MM-dd".   |
| `dueDate`          | String  | The new due date for the task.                   | Optional, format: "yyyy-MM-dd".   |
| `estimatedWorkTime`| Integer | The new estimated work time in hours.            | Optional.                         |
| `actualWorkTime`   | Integer | The actual work time spent on the task in hours. | Optional.                         |
| `assignees`        | Array   | Array of user IDs to be assigned to the task.    | Optional, can be an empty array.  |

#### Example Request

```json
{
  "taskId": 456,
  "status": "IN_PROGRESS",
  "actualWorkTime": 2,
  "assignees": [789, 101]
}
```

#### Response

- **Success Response (HTTP 200):**

```json
{
  "success": true,
  "message": "Task details and assignees updated successfully",
  "data": {
    "id": 456,
    "name": "Implement login functionality",
    "description": "Create the login form and handle authentication",
    "priority": "HIGH",
    "status": "IN_PROGRESS",
    "starDate": "2023-06-01",
    "dueDate": "2023-06-15",
    "estimatedWorkTime": 8,
    "actualWorkTime": 2,
    "assignedTo": [789, 101]
  }
}
```

- **Error Responses:**
  - For missing fields or malformed requests (HTTP 400):

```json
{
  "success": false,
  "message": "Missing required field: taskId"
}
```

  - For invalid values (HTTP 400):

```json
{
  "success": false,
  "message": "Invalid status value. Valid values are: NOT_STARTED, IN_PROGRESS, UNDER_REVIEW, TESTING, DONE"
}
```

  - For authentication failures (HTTP 401):

```json
{
  "success": false,
  "message": "Not signed in."
}
```

  - For non-existent tasks (HTTP 404):

```json
{
  "success": false,
  "message": "Task not found"
}
```

  - For server errors (HTTP 500):

```json
{
  "success": false,
  "message": "Error updating task: [error details]"
}
```

---

### 3. Delete Task

- **Endpoint:** `/project/task/delete`
- **Method:** `POST`
- **Authentication:** Required

This endpoint allows authenticated users to delete an existing task.

#### Request Body

The request must be a JSON object containing the following fields:

| Field    | Type    | Description                                      | Validation Summary                |
|----------|---------|--------------------------------------------------|-----------------------------------|
| `taskId` | Integer | The ID of the task to delete.                    | Required, must be a valid ID.     |

#### Example Request

```json
{
  "taskId": 456
}
```

#### Response

- **Success Response (HTTP 200):**

```json
{
  "success": true,
  "message": "Task deleted successfully"
}
```

- **Error Responses:**
  - For missing fields or malformed requests (HTTP 400):

```json
{
  "success": false,
  "message": "Missing required field: taskId"
}
```

  - For invalid ID formats (HTTP 400):

```json
{
  "success": false,
  "message": "Invalid task ID format"
}
```

  - For authentication failures (HTTP 401):

```json
{
  "success": false,
  "message": "Not signed in."
}
```

  - For non-existent tasks (HTTP 404):

```json
{
  "success": false,
  "message": "Task not found"
}
```

  - For server errors (HTTP 500):

```json
{
  "success": false,
  "message": "Error deleting task: [error details]"
}
```

---

## Additional Notes

- **Task Ownership:**  
  Tasks belong to user stories, which in turn belong to projects. The permission model follows the project's permission model.

- **Task Assignees:**  
  Task assignees are represented by their user IDs. The API does not validate whether these IDs correspond to existing users, so it's the client's responsibility to ensure valid user IDs are provided.

- **Partial Updates:**  
  The update endpoint supports partial updates. You can include only the fields you want to update in the request body. For example, if you only want to update the task status, you can include only the `taskId` and `status` fields.

- **Error Handling:**  
  All endpoints return clear error messages and appropriate HTTP status codes in case of issues with the request or processing.

---

This documentation covers the primary endpoints for task management in the FlowHive API. For further details on other endpoints or additional functionality, please refer to the complete API reference.
