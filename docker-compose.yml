services:
  backend-app:
    container_name: flowhive-backend
    restart: always
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      ENVIRONMENT: production
      POSTGRESQL_URI: *******************************************
      POSTGRES_USER: myuser
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    depends_on:
      - postgres-db

  postgres-db:
    image: postgres:15
    container_name: flowhive-postgres-db
    environment:
      POSTGRES_DB: flowhive
      POSTGRES_USER: myuser
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
