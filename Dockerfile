# Use Maven image for build with JDK 21
FROM maven:3.8.8-eclipse-temurin-21 AS builder

# Set working directory
WORKDIR /backendApp

# Copy Maven project files
COPY pom.xml .
COPY src ./src

# Build the project
RUN mvn clean package -DskipTests

# Use a lightweight JDK 21 image for runtime
FROM eclipse-temurin:21-jre-alpine
WORKDIR /backendApp

# Copy the built jar from the builder stage
COPY --from=builder /backendApp/target/flowhive-backend-1.0-SNAPSHOT.jar app.jar
EXPOSE 8080
CMD ["java", "-jar", "app.jar"]